import React, { useState, useEffect, useMemo } from 'react';
import { Trash2, ChevronDown, ChevronLeft, ChevronRight } from 'lucide-react';
import { usePosOrderItemsQuery, useDeleteOrderMutation } from "../../../store/api/orderItemApi";
import Swal from 'sweetalert2';

interface Product {
  _id: string;
  name: string;
  price: number;
  quantity?: number;
}

interface ProductWithQty {
  productId: string;
  qty: number;
  price: number;
  discount: number;
}
interface Table {
  tableName?: string;
  tableNo?: string;
}

interface ApiOrder {
  _id: string;
  OrderNumber?: string;
  Status: string;
  customername?: string;
  grandTotal: number;
  product: Product[];
  productWithQty: ProductWithQty[];
    table: Table[] | Table;
  units: number;
  orderStatus: string;
  orderNo?: string;
  employeeId?: {
    firstName: string;
    lastName: string;
  };
}

interface Order {
  id: string;
  operator?: string;
  status: string;
  table?: string;
  products?: number;
  price?: string;
  _id?: string;
}

const AllPosOrders: React.FC = () => {
  const [expandedOrderId, setExpandedOrderId] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [orders, setOrders] = useState<Order[]>([]);
  const [isDeleting, setIsDeleting] = useState(false);

  const ITEMS_PER_PAGE = 5;

  const userId = localStorage.getItem("userId") || "";
  const { data: AllOrders, isLoading, error } = usePosOrderItemsQuery(userId);
  const [deleteOrder] = useDeleteOrderMutation();

  console.log('AllOrders', AllOrders);

  const handleDelete = (id: string) => {
    return (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();
      
      const currentOrder = orders.find(order => order.id === id);
      let originalId = id;
      
      if (currentOrder && currentOrder._id) {
        originalId = currentOrder._id;
      } else if (AllOrders) {
        const apiOrder = AllOrders.find((order: ApiOrder) => 
          (order.OrderNumber || order._id.substring(0, 6)) === id
        );
        if (apiOrder) {
          originalId = apiOrder._id;
        }
      }
      
      Swal.fire({
        title: 'Are you sure?',
        text: 'Do you want to delete this order?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
      cancelButtonColor: '#6b7280',
        confirmButtonText: 'Yes, delete it!',
        cancelButtonText: 'No, cancel!'
      }).then((result) => {
        if (result.isConfirmed) {
          confirmDeleteOrder(id, originalId);
        }
      });
    };
  };

  const confirmDeleteOrder = async (id: string, originalId: string) => {
    setIsDeleting(true);
    try {
      await deleteOrder(originalId).unwrap();
      
      setOrders(prevOrders => prevOrders.filter(order => order.id !== id));
     
      Swal.fire({
        title: 'Deleted!',
        text: 'Order has been deleted successfully.',
        icon: 'success',
        confirmButtonText: 'OK'
      });
      
    } catch (error) {
      console.error('Failed to delete order:', error);
      Swal.fire({
        title: 'Error!',
        text: 'Failed to delete order. Please try again.',
        icon: 'error',
        confirmButtonText: 'OK'
      });
    } finally {
      setIsDeleting(false);
    }
  };
  useEffect(() => {
  if (AllOrders) {
    const posOrders = AllOrders
      .filter((order: ApiOrder) => order.orderStatus === 'pos')
      .map((order: ApiOrder) => {
        // Extract table information
        let tableDisplay = 'No Table';
        if (Array.isArray(order.table) && order.table.length > 0) {
          const firstTable = order.table[0];
          tableDisplay = firstTable.tableName || firstTable.tableNo || 'No Table';
        } else if (order.table && typeof order.table === 'object' && !Array.isArray(order.table)) {
          const tableObj = order.table as Table;
          tableDisplay = tableObj.tableName || tableObj.tableNo || 'No Table';
        }

          return {
            id: order.OrderNumber || order._id.substring(0, 6),
            operator: order.employeeId ? 
              `${order.employeeId.firstName} ${order.employeeId.lastName}` : 'Admin',
            status: order.Status === 'in progress' ? 'In Progress' : order.Status,
            table: tableDisplay,
            products: order.product?.length || 0,
            price: `$${order.grandTotal?.toFixed(2) || '0.00'}`,
            _id: order._id
          };
        });

      setOrders(posOrders);
    }
  }, [AllOrders]);

  const { totalPages, indexOfFirstOrder, indexOfLastOrder, currentOrders } = useMemo(() => {
    const totalPages = Math.ceil(orders.length / ITEMS_PER_PAGE);
    const indexOfLastOrder = currentPage * ITEMS_PER_PAGE;
    const indexOfFirstOrder = indexOfLastOrder - ITEMS_PER_PAGE;
    const currentOrders = orders.slice(indexOfFirstOrder, indexOfLastOrder);
    
    return { 
      totalPages, 
      indexOfFirstOrder, 
      indexOfLastOrder, 
      currentOrders 
    };
  }, [orders, currentPage]);

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'In Progress':
      case 'in progress':
        return 'bg-[#FFF9EB] text-[#F0B433] border border-[#F0B433]';
      case 'Completed':
        return 'bg-[#ECFEED] text-[#13C91B] border border-[#13C91B]';
      case 'Pending':
        return 'bg-blue-100 text-blue-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  const toggleOrderDetails = (orderId: string) => {
    setExpandedOrderId(expandedOrderId === orderId ? null : orderId);
  };

  const renderOrderDetails = (orderId: string) => {
    const orderDetails = AllOrders?.find((order: ApiOrder) => 
      (order.OrderNumber || order._id.substring(0, 6)) === orderId
    );
    
    if (!orderDetails) return null;
    
    const customerName = orderDetails.customername || "Guest";
    const orderNo = orderDetails.orderNo || `#${orderDetails._id.substring(0, 12)}`;
    
    return (
      <div className="bg-gray-50 p-4 border-t border-gray-200">
        <div className="grid grid-cols-3 gap-4">
          <div>
            <div className="text-sm font-medium text-gray-700">Customer Name</div>
            <div className="text-sm text-gray-900">{customerName}</div>
          </div>
          <div>
            <div className="text-sm font-medium text-gray-700">Order Items</div>
            <div className="text-sm text-gray-900">
              {orderDetails.product?.map((product: Product, index: number) => (
                <div key={index}>
                  {product.name} <span className="text-gray-500">x{product.quantity || 1}</span>
                </div>
              ))}
              {(!orderDetails.product || orderDetails.product.length === 0) && (
                <div className="text-gray-500">No items</div>
              )}
            </div>
          </div>
          <div>
            <div className="text-sm font-medium text-gray-700">Order No</div>
            <div className="text-sm text-gray-900">{orderNo}</div>
          </div>
        </div>
      </div>
    );
  };

  const renderPageNumbers = () => {
    const pageNumbers = [];
    const MAX_VISIBLE_PAGES = 5;

    if (totalPages <= MAX_VISIBLE_PAGES) {
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(i);
      }
    } else {
      pageNumbers.push(1);
      const startPage = Math.max(2, currentPage - 1);
      const endPage = Math.min(totalPages - 1, currentPage + 1);

      if (startPage > 2) {
        pageNumbers.push('...');
      }

      for (let i = startPage; i <= endPage; i++) {
        pageNumbers.push(i);
      }

      if (endPage < totalPages - 1) {
        pageNumbers.push('...');
      }

      if (totalPages > 1) {
        pageNumbers.push(totalPages);
      }
    }

    return pageNumbers.map((page, index) => {
      if (page === '...') {
        return (
          <span key={`ellipsis-${index}`} className="mx-1 text-gray-500">...</span>
        );
      }
      return (
        <button 
          key={`page-${page}`} 
          onClick={() => setCurrentPage(page as number)}
          className={`w-8 h-8 flex items-center justify-center rounded border ${
            currentPage === page 
              ? 'border-orange-500 bg-orange-500 text-white' 
              : 'border-gray-300 hover:bg-gray-100'
          }`}
        >
          {page}
        </button>
      );
    });
  };

  if (isLoading) {
    return (
    <div className="flex justify-center items-start bg-white h-screen pt-[20vh]">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500 mb-4"></div>
          <p className="text-gray-600 font-medium">Loading orders...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="py-4 px-4 text-center">
        <p className="text-red-500">Error loading POS orders. Please try again.</p>
      </div>
    );
  }

  return (
    <div>
      {/* Table container */}
      <div className="rounded-lg overflow-hidden border border-gray-100">
        <div className="overflow-x-auto custom-scrollbar">
          {/* Table Header */}
          <div className="bg-orange-50 grid grid-cols-6 py-3 px-4 min-w-[800px]">
            <div className="text-sm font-medium text-gray-800">Order No</div>
            <div className="text-sm font-medium text-gray-800">Operator</div>
            <div className="text-sm font-medium text-gray-800">Status</div>
            <div className="text-sm font-medium text-gray-800">Table No</div>
            <div className="text-sm font-medium text-gray-800">No of Products</div>
            <div className="text-sm font-medium text-gray-800">Price</div>
          </div>
          
          {/* Table Body */}
          <div className="bg-white">
            {currentOrders.length === 0 ? (
              <div className="py-4 px-4 text-center">
                <p>No POS orders available.</p>
              </div>
            ) : (
              currentOrders.map((order: Order) => (
                <React.Fragment key={order.id}>
                  <div className="grid grid-cols-6 py-4 px-4 items-center border-b border-b-gray-200 hover:bg-orange-50 min-w-[800px]">
                    <div className="text-sm text-gray-900">{order.id}</div>
                    <div className="text-sm text-gray-700">{order.operator}</div>
                    <div className="text-sm">
                      <span className={`px-4 py-1.5 text-xs inline-flex items-center rounded-lg ${getStatusColor(order.status)}`}>
                        {order.status}
                      </span>
                    </div>
                    <div className="text-sm text-gray-700">{order.table}</div>
                    <div className="text-sm text-gray-700">{order.products}</div>
                    <div className="text-sm text-gray-700 flex justify-between items-center">
                      <span className="text-gray-700 font-medium">{order.price}</span>
                      <div className="flex items-center space-x-2">
                        <button 
                          onClick={handleDelete(order.id)}
                          className="text-red-500 hover:text-red-700"
                          aria-label="Delete order"
                          disabled={isDeleting}
                        >
                          <div className="relative group inline-block">
                                                       <Trash2
                                                         size={20}
                                                         className="cursor-pointer text-red-500 hover:text-red-700 transition-colors"
                                                       />
                                                       <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-1 hidden group-hover:block bg-gray-800 text-white text-xs px-2 py-1 rounded whitespace-nowrap">
                                                         Delete
                                                         <div className="absolute bottom-full left-1/2 -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-0 border-b-4 border-l-transparent border-r-transparent border-b-gray-800"></div>
                                                       </div>
                                                     </div>
                        </button>
                        <button 
                          className="text-gray-500 hover:text-gray-700"
                          aria-label="View details"
                          onClick={() => toggleOrderDetails(order.id)}
                        >
                        <div className="relative group inline-block">
                          <ChevronDown 
                            size={20}
                            className="cursor-pointer text-gray-500 hover:text-gray-700 transition-colors"
                          />
                          <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-1 hidden group-hover:block bg-gray-800 text-white text-xs px-2 py-1 rounded whitespace-nowrap">
                            Expand
                            <div className="absolute bottom-full left-1/2 -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-0 border-b-4 border-l-transparent border-r-transparent border-b-gray-800"></div>
                          </div>
                        </div>
                        </button>
                      </div>
                    </div>
                  </div>
                  {expandedOrderId === order.id && renderOrderDetails(order.id)}
                </React.Fragment>
              ))
            )}
          </div>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex flex-col sm:flex-row items-center justify-between mt-4">
        <div className="flex items-center space-x-2">
          <button 
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
            className={`w-8 h-8 flex items-center justify-center rounded border border-gray-300 ${
              currentPage === 1 ? 'text-gray-400 cursor-not-allowed' : 'hover:bg-gray-100'
            }`}
            aria-label="Previous page"
          >
            <ChevronLeft size={16} />
          </button>
          
          <div className="flex space-x-1">
            {renderPageNumbers()}
          </div>
          
          <button 
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
            className={`w-8 h-8 flex items-center justify-center rounded border border-gray-300 ${
              currentPage === totalPages ? 'text-gray-400 cursor-not-allowed' : 'hover:bg-gray-100'
            }`}
            aria-label="Next page"
          >
            <ChevronRight size={16} />
          </button>
        </div>
        <div className="text-sm text-gray-600 mb-2 sm:mb-0">
          Showing {indexOfFirstOrder + 1}-{Math.min(indexOfLastOrder, orders.length)} of {orders.length} records
        </div>
      </div>
    </div>
  );
};

export default AllPosOrders;