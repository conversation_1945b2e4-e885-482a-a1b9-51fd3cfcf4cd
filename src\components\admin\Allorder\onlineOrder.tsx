import React, { useState, useEffect, useMemo } from "react";
import { Trash2, ChevronDown, ChevronLeft, ChevronRight } from "lucide-react";
import {
  useOnlineOrderItemsQuery,
  useDeleteOnlineOrderMutation,
} from "../../../store/api/orderItemApi";
import Swal from "sweetalert2";

interface Product {
  _id: string;
  name: string;
  price: number;
  quantity?: number;
  ProductId?: string;
}

interface Customer {
  _id: string;
  CustomerId?: string;
  FirstName: string;
  LastName: string;
  Email?: string;
  Phone?: string;
  Address?: string;
  Address3?: string;
  City?: string;
  State?: string;
}

interface Tax {
  name: string;
  value: number;
}

interface ApiOnlineOrder {
  _id: string;
  Amount: number;
  OrderNumber?: string;
  PaymentStatus: string;
  Status: string;
  couponOffer: any[];
  createdAt: string;
  customerId: Customer;
  deliveryfee: number;
  loyalityOffer: any[];
  note: string;
  orderNo: string;
  orderStatus: string;
  orderType: string;
  paymentIntentId: string;
  product: Product[];
  tax: Tax[];
  updatedAt: string;
  userId: {
    _id: string;
    name: string;
    email: string;
  };
}

interface OnlineOrder {
  id: string;
  orderNumber: string;
  productCount: number;
  address: string;
  phone: string;
  email: string;
  status: string;
  deliveryFee: string;
  totalAmount: string;
  orderStatus: string;
  _id: string;
  customerName: string;
  products: Product[];
  note: string;
  createdAt: string;
  paymentStatus: string;
}

const AllOnlineOrderItem: React.FC = () => {
  const [expandedOrderId, setExpandedOrderId] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [isDeleting, setIsDeleting] = useState(false);

  const ITEMS_PER_PAGE = 5;

  const userId = localStorage.getItem("userId") || "";
  const {
    data: AllOnlineOrders,
    isLoading,
    error,
    refetch,
  } = useOnlineOrderItemsQuery(userId);
  const [deleteOrder] = useDeleteOnlineOrderMutation();
  const formatAddress = (customer: Customer): string => {
    const addressParts: string[] = [];
    if (customer.City) {
      addressParts.push(customer.City);
    }
    if (customer.State) {
      addressParts.push(customer.State);
    }
    if (customer.Address3) {
      addressParts.push(customer.Address3);
    }
    if (addressParts.length === 0 && customer.Address) {
      return customer.Address;
    }

    return addressParts.length > 0 ? addressParts.join(", ") : "N/A";
  };
  const orders = useMemo(() => {
    if (!AllOnlineOrders) return [];

    // Create a Set to track unique order IDs and prevent duplicates
    const seenOrderIds = new Set<string>();

    const transformedOrders = AllOnlineOrders.filter(
      (order: ApiOnlineOrder) => {
        // Use the original _id as the unique identifier
        if (seenOrderIds.has(order._id)) {
          return false; // Skip duplicate
        }
        seenOrderIds.add(order._id);
        return true;
      }
    ).map((order: ApiOnlineOrder) => ({
      id: order._id,
      orderNumber:
        order.OrderNumber || order.orderNo || `#${order._id.substring(0, 6)}`,
      productCount: order.product?.length || 0,
      address: formatAddress(order.customerId),
      phone: order.customerId?.Phone || "N/A",
      email: order.customerId?.Email || "N/A",
      status: order.Status,
      deliveryFee: `$${order.deliveryfee?.toFixed(2) || "0.00"}`,
      totalAmount: `$${order.Amount?.toFixed(2) || "0.00"}`,
      orderStatus: order.orderStatus === "done" ? "Completed" : "In Progress",
      _id: order._id,
      customerName:
        `${order.customerId?.FirstName || ""} ${
          order.customerId?.LastName || ""
        }`.trim() || "Guest",
      products: order.product || [],
      note: order.note || "",
      createdAt: order.createdAt,
      paymentStatus: order.PaymentStatus,
    }));

    return transformedOrders;
  }, [AllOnlineOrders]);

  const handleDelete = (id: string) => {
    return (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();

      Swal.fire({
        title: "Are you sure?",
        text: "Do you want to delete this order?",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#6b7280",
        confirmButtonText: "Yes, delete it!",
        cancelButtonText: "No, cancel!",
      }).then((result) => {
        if (result.isConfirmed) {
          confirmDeleteOrder(id);
        }
      });
    };
  };

  const confirmDeleteOrder = async (id: string) => {
    setIsDeleting(true);
    try {
      await deleteOrder(id).unwrap();

      // Refetch the data to get the updated list
      await refetch();

      Swal.fire({
        title: "Deleted!",
        text: "Order has been deleted successfully.",
        icon: "success",
        confirmButtonText: "OK",
      });
    } catch (error) {
      console.error("Failed to delete order:", error);
      Swal.fire({
        title: "Error!",
        text: "Failed to delete order. Please try again.",
        icon: "error",
        confirmButtonText: "OK",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  // Reset to first page when orders change
  useEffect(() => {
    setCurrentPage(1);
  }, [orders.length]);

  const { totalPages, indexOfFirstOrder, indexOfLastOrder, currentOrders } =
    useMemo(() => {
      const totalPages = Math.ceil(orders.length / ITEMS_PER_PAGE);
      const indexOfLastOrder = currentPage * ITEMS_PER_PAGE;
      const indexOfFirstOrder = indexOfLastOrder - ITEMS_PER_PAGE;
      const currentOrders = orders.slice(indexOfFirstOrder, indexOfLastOrder);

      return {
        totalPages,
        indexOfFirstOrder,
        indexOfLastOrder,
        currentOrders,
      };
    }, [orders, currentPage]);

  const getStatusColor = (status: string): string => {
    switch (status) {
      case "In Progress":
      case "in progress":
      case "preparing":
        return "bg-[#FFF9EB] text-[#F0B433] border border-[#F0B433]";
      case "Completed":
      case "done":
        return "bg-[#ECFEED] text-[#13C91B] border border-[#13C91B]";
      case "Pending":
        return "bg-blue-100 text-blue-700 border border-blue-300";
      case "patronpal order":
        return "bg-[#ECFEED] text-[#13C91B] border border-[#13C91B]";
      case "Personal Website":
        return "bg-[#FFF9EB] text-[#F0B433] border border-[#F0B433]";
      case "online":
        return "bg-green-100 text-green-700 border border-green-300";
      default:
        return "bg-gray-100 text-gray-700 border border-gray-300";
    }
  };

  const toggleOrderDetails = (orderId: string) => {
    setExpandedOrderId(expandedOrderId === orderId ? null : orderId);
  };

  const renderOrderDetails = (orderId: string) => {
    const orderDetails = orders.find((order: any) => order.id === orderId);

    if (!orderDetails) return null;

    return (
      <div className="bg-gray-50 p-4 border-t border-gray-200  min-w-[1250px]">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <div className="text-sm font-medium text-gray-700 mb-2">
              Customer Name
            </div>
            <div className="text-sm text-gray-900 mb-3">
              {orderDetails.customerName}
            </div>
          </div>

          <div>
            <div className="text-sm font-medium text-gray-700 mb-2">
              Order Items
            </div>
            <div className="text-sm text-gray-900 mb-3">
              {orderDetails.products && orderDetails.products.length > 0 ? (
                orderDetails.products.map((product: Product, index: number) => (
                  <div key={index} className="flex justify-between">
                    <span>{product.name}</span>
                    <span className="text-gray-500">
                      {product.quantity || 1}
                    </span>
                  </div>
                ))
              ) : (
                <div className="text-gray-500">No items</div>
              )}
            </div>
          </div>

          <div>
            <div className="text-sm font-medium text-gray-700 mb-2">
              Customer Note
            </div>
            <div className="text-sm text-gray-900 mb-3">
              {orderDetails.note || "No notes"}
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderPageNumbers = () => {
    const pageNumbers = [];
    const MAX_VISIBLE_PAGES = 5;

    if (totalPages <= MAX_VISIBLE_PAGES) {
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(i);
      }
    } else {
      pageNumbers.push(1);
      const startPage = Math.max(2, currentPage - 1);
      const endPage = Math.min(totalPages - 1, currentPage + 1);

      if (startPage > 2) {
        pageNumbers.push("...");
      }

      for (let i = startPage; i <= endPage; i++) {
        pageNumbers.push(i);
      }

      if (endPage < totalPages - 1) {
        pageNumbers.push("...");
      }

      if (totalPages > 1) {
        pageNumbers.push(totalPages);
      }
    }

    return pageNumbers.map((page, index) => {
      if (page === "...") {
        return (
          <span key={`ellipsis-${index}`} className="mx-1 text-gray-500">
            ...
          </span>
        );
      }
      return (
        <button
          key={`page-${page}`}
          onClick={() => setCurrentPage(page as number)}
          className={`w-8 h-8 flex items-center justify-center rounded border ${
            currentPage === page
              ? "border-orange-500 bg-orange-500 text-white"
              : "border-gray-300 hover:bg-gray-100"
          }`}
        >
          {page}
        </button>
      );
    });
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-start bg-white h-screen pt-[20vh]">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500 mb-4"></div>
          <p className="text-gray-600 font-medium">Loading orders...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="py-4 px-4 text-center">
        <p className="text-red-500">
          Error loading online orders. Please try again.
        </p>
      </div>
    );
  }

  return (
    <div>
      {/* Table container */}
      <div className="rounded-lg overflow-hidden border border-gray-100">
        <div className="overflow-x-auto custom-scrollbar">
          {/* Table Header */}
          <div className="bg-orange-50 grid grid-cols-[100px_80px_200px_120px_200px_150px_100px_100px_180px] py-3 px-4 min-w-[1250px]">
            <div className="text-sm font-medium text-gray-800 px-2">
              Order No
            </div>
            <div className="text-sm font-medium text-gray-800 px-2">
              Products
            </div>
            <div className="text-sm font-medium text-gray-800 px-2">
              Address
            </div>
            <div className="text-sm font-medium text-gray-800 px-2">Phone</div>
            <div className="text-sm font-medium text-gray-800 px-2">Email</div>
            <div className="text-sm font-medium text-gray-800 px-2">Status</div>
            <div className="text-sm font-medium text-gray-800 px-2">
              Delivery Fee
            </div>
            <div className="text-sm font-medium text-gray-800 px-2">
              Total Amount
            </div>
            <div className="text-sm font-medium text-gray-800 px-2">
              Order Status
            </div>
          </div>

          <div className="bg-white">
            {currentOrders.length === 0 ? (
              <div className="py-4 px-4 text-center">
                <p>No online orders available.</p>
              </div>
            ) : (
              currentOrders.map((order: OnlineOrder) => (
                <React.Fragment key={order.id}>
                  {/* Table Row - Matching grid columns with header */}
                  <div className="grid grid-cols-[100px_80px_200px_120px_200px_150px_100px_100px_180px] py-4 px-4 items-center border-b border-b-gray-200 hover:bg-orange-50 min-w-[1250px]">
                    <div className="text-sm text-gray-900 font-medium px-2 truncate">
                      {order.orderNumber}
                    </div>
                    <div className="text-sm text-gray-700 px-2">
                      {order.productCount}
                    </div>
                    <div
                      className="text-sm text-gray-700 px-2 truncate"
                      title={order.address}
                    >
                      {order.address}
                    </div>
                    <div className="text-sm text-gray-700 px-2 truncate">
                      {order.phone}
                    </div>
                    <div
                      className="text-sm text-gray-700 px-2 truncate"
                      title={order.email}
                    >
                      {order.email}
                    </div>
                    <div className="text-sm px-2">
                      <span
                        className={`px-3 py-1.5 text-xs inline-flex items-center rounded-lg ${getStatusColor(
                          order.status
                        )}`}
                      >
                        {order.status}
                      </span>
                    </div>
                    <div className="text-sm text-gray-700 font-medium px-2">
                      {order.deliveryFee}
                    </div>
                    <div className="text-sm text-gray-900 font-medium px-2">
                      {order.totalAmount}
                    </div>
                    <div className="text-sm flex justify-between items-center px-2">
                      <span
                        className={`px-3 py-1.5 text-xs inline-flex items-center rounded-lg ${getStatusColor(
                          order.orderStatus
                        )}`}
                      >
                        {order.orderStatus}
                      </span>
                      <div className="flex items-center space-x-2 ml-2">
                        <button
                          onClick={handleDelete(order._id)}
                          className="text-red-500 hover:text-red-700"
                          aria-label="Delete order"
                          disabled={isDeleting}
                        >
                          <div className="relative group inline-block">
                            <Trash2
                              size={20}
                              className="cursor-pointer text-red-500 hover:text-red-700 transition-colors"
                            />
                            <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-1 hidden group-hover:block bg-gray-800 text-white text-xs px-2 py-1 rounded whitespace-nowrap">
                              Delete
                              <div className="absolute bottom-full left-1/2 -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-0 border-b-4 border-l-transparent border-r-transparent border-b-gray-800"></div>
                            </div>
                          </div>
                        </button>
                        <button
                          className="text-gray-500 hover:text-gray-700"
                          aria-label="View details"
                          onClick={() => toggleOrderDetails(order.id)}
                        >
                          <div className="relative group inline-block">
                            <ChevronDown
                              size={20}
                              className="cursor-pointer text-gray-500 hover:text-gray-700 transition-colors"
                            />
                            <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-1 hidden group-hover:block bg-gray-800 text-white text-xs px-2 py-1 rounded-lg whitespace-nowrap">
                              Expand
                              <div className="absolute bottom-full left-1/2 -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-0 border-b-4 border-l-transparent border-r-transparent border-b-gray-800"></div>
                            </div>
                          </div>
                        </button>
                      </div>
                    </div>
                  </div>
                  {expandedOrderId === order.id && renderOrderDetails(order.id)}
                </React.Fragment>
              ))
            )}
          </div>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex flex-col sm:flex-row items-center justify-between mt-4">
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
            className={`w-8 h-8 flex items-center justify-center rounded border border-gray-300 ${
              currentPage === 1
                ? "text-gray-400 cursor-not-allowed"
                : "hover:bg-gray-100"
            }`}
            aria-label="Previous page"
          >
            <ChevronLeft size={16} />
          </button>

          <div className="flex space-x-1">{renderPageNumbers()}</div>

          <button
            onClick={() =>
              setCurrentPage((prev) => Math.min(prev + 1, totalPages))
            }
            disabled={currentPage === totalPages}
            className={`w-8 h-8 flex items-center justify-center rounded border border-gray-300 ${
              currentPage === totalPages
                ? "text-gray-400 cursor-not-allowed"
                : "hover:bg-gray-100"
            }`}
            aria-label="Next page"
          >
            <ChevronRight size={16} />
          </button>
        </div>
        <div className="text-sm text-gray-600 mb-2 sm:mb-0">
          Showing {indexOfFirstOrder + 1}-
          {Math.min(indexOfLastOrder, orders.length)} of {orders.length} records
        </div>
      </div>
    </div>
  );
};

export default AllOnlineOrderItem;
