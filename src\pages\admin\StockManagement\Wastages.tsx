import { useState, useEffect } from "react";
import { Eye, Trash2, ChevronLeft, ChevronRight, Plus, Search } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useGetWastagesQuery, useDeleteWastageMutation } from "../../../store/api/wastagesApi";
import { toast } from 'react-toastify';

export default function Wastages() {
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(5);
  const [showModal, setShowModal] = useState(false);
  const [wastageToDelete, setWastageToDelete] = useState<string | null>(null);

  const navigate = useNavigate();
  const userId = localStorage.getItem('userId') || '';

 
  const { data: wastagesData, isLoading, error, refetch } = useGetWastagesQuery(userId);
  
  // Use the mutation hook for deletion
  const [deleteWastage, { isLoading: isDeleting }] = useDeleteWastageMutation();

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  const handleDelete = (id: string) => {
    setWastageToDelete(id);
    setShowModal(true);
  };

  const confirmDelete = async () => {
    if (wastageToDelete) {
      try {
        await deleteWastage(wastageToDelete).unwrap();
        toast.success('Wastage deleted successfully');
        refetch();
        setShowModal(false);
      } catch (error) {
        console.error('Failed to delete wastage:', error);
        toast.error('Failed to delete wastage');
        setShowModal(false);
      }
    }
  };

  const handleAddWastage = () => {
    navigate('/admin/stock-management/wastages/wastages-form');
  };

  const handleView = (id: string) => {
    navigate(`/admin/stock-management/wastages/wastage-details/${id}`);
  };

  // Filter wastages based on search term
  const filteredWastages = wastagesData?.filter(wastage =>
    wastage.IngredientName.IngredientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    wastage.ReasonOfWastage.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentWastages = filteredWastages.slice(indexOfFirstItem, indexOfLastItem);

  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);

  useEffect(() => {
    if (error) {
      toast.error('Failed to fetch wastages');
      console.error('Error fetching wastages:', error);
    }
  }, [error]);

  // Format the date from ISO string to a more readable format
  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="p-4 bg-gray-50">
      {/* Delete Confirmation Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black/50 flex justify-center items-center z-50">
          <div className="bg-white p-8 rounded-xl shadow-xl w-full max-w-md text-center relative">
            <div className="flex justify-center mb-4">
              <div className="bg-orange-100 text-orange-500 p-4 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01M12 5c.512 0 1.023.195 1.414.586C13.805 6.977 14 7.488 14 8s-.195 1.023-.586 1.414A1.993 1.993 0 0112 10a1.993 1.993 0 01-1.414-.586A1.993 1.993 0 0110 8c0-.512.195-1.023.586-1.414A1.993 1.993 0 0112 5z" />
                </svg>
              </div>
            </div>
            <h3 className="text-2xl font-semibold text-gray-800">Are you sure?</h3>
            <p className="mt-2 text-gray-600">Do you want to delete this wastage record?</p>
            <div className="mt-6 flex justify-center gap-4">
              <button
                onClick={confirmDelete}
                className="bg-orange-600 hover:bg-orange-700 text-white px-5 py-2 rounded-md font-medium"
              >
                Yes, delete it!
              </button>
              <button
                onClick={() => setShowModal(false)}
                className="bg-gray-200 hover:bg-gray-300 text-gray-800 px-5 py-2 rounded-md font-medium"
              >
                No, cancel!
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="flex justify-between items-center md:flex-row flex-col p-2 mb-2 border border-gray-200 bg-white rounded-2xl">
        <h1 className="md:text-3xl text-xl p-3 font-bold text-gray-800">Wastages</h1>
        <div className="flex md:flex-row flex-col-reverse gap-2">
          <div className="relative">
            <input
              type="text"
              placeholder="Search Wastages"
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg w-64 focus:outline-none focus:ring-2 focus:ring-orange-500"
              value={searchTerm}
              onChange={handleSearch}
            />
            <Search className="absolute left-3 top-2.5 text-gray-400 h-5 w-5" />
          </div>
          <button
            onClick={handleAddWastage}
            className="bg-orange-500 hover:bg-orange-600 text-white font-medium px-4 py-2 rounded-lg flex items-center gap-1"
          >
            Add Wastages
            <Plus size={20} />
          </button>
        </div>
      </div>

      {isLoading ? (
        <p className="text-center text-gray-600 py-8">Loading wastages...</p>
      ) : error ? (
        <p className="text-center text-red-500 py-8">Failed to load wastages.</p>
      ) : (
        <>
          <div className="bg-white shadow-sm border border-gray-200 rounded-2xl">
            <div className="overflow-x-auto rounded-2xl">
              <table className="w-full">
                <thead className="bg-orange-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-sm font-medium text-gray-700">
                      No
                    </th>
                    <th className="px-6 py-3 text-left text-sm font-medium text-gray-700">
                      Ingredient
                    </th>
                    <th className="px-6 py-3 text-left text-sm font-medium text-gray-700">
                      Date
                    </th>
                    <th className="px-6 py-3 text-left text-sm font-medium text-gray-700">
                      Quantity
                    </th>
                    <th className="px-6 py-3 text-right text-sm font-medium text-gray-700">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white">
                  {currentWastages.length > 0 ? (
                    currentWastages.map((wastage, index) => (
                      <tr key={wastage.id} className="hover:bg-orange-50">
                        <td className="px-6 py-4 text-sm text-gray-800">
                          {indexOfFirstItem + index + 1}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-800">
                          {wastage.IngredientName.IngredientName}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-800">
                          {formatDate(wastage.createdAt)}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-800">
                          {wastage.Quantity}
                        </td>
                        <td className="px-6 py-4 text-sm flex gap-2 justify-end">
                          <button
                            onClick={() => handleView(wastage.id)}
                            className="text-orange-500 hover:text-orange-700"
                            disabled={isDeleting}
                          >
                            <Eye size={18} />
                          </button>
                          <button
                            onClick={() => handleDelete(wastage.id)}
                            className={`text-red-500 hover:text-red-700 ${isDeleting ? "cursor-not-allowed opacity-50" : ""}`}
                            disabled={isDeleting}
                          >
                            <Trash2 size={18} />
                          </button>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={5} className="px-6 py-4 text-center text-gray-500">
                        No wastages found.
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
          
          {/* Pagination */}
          {filteredWastages.length > 0 && (
            <div className="flex justify-start items-center space-x-2 p-4">
              <button
                className={`p-1 rounded-md border border-gray-200 ${
                  currentPage === 1
                    ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                    : "hover:bg-gray-100"
                }`}
                onClick={() => paginate(Math.max(currentPage - 1, 1))}
                disabled={currentPage === 1}
              >
                <ChevronLeft size={18} className="text-gray-600" />
              </button>

              {Array.from(
                { length: Math.ceil(filteredWastages.length / itemsPerPage) },
                (_, i) => i + 1
              ).map((number) => (
                <button
                  key={number}
                  onClick={() => paginate(number)}
                  className={`w-8 h-8 rounded-md flex items-center justify-center border ${
                    currentPage === number
                      ? "bg-orange-500 text-white"
                      : "bg-white text-gray-700 hover:bg-gray-50"
                  }`}
                >
                  {number}
                </button>
              ))}

              <button
                className={`p-1 rounded-md border border-gray-200 ${
                  indexOfLastItem >= filteredWastages.length
                    ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                    : "hover:bg-gray-100"
                }`}
                onClick={() => paginate(currentPage + 1)}
                disabled={indexOfLastItem >= filteredWastages.length}
              >
                <ChevronRight size={18} className="text-gray-600" />
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
}