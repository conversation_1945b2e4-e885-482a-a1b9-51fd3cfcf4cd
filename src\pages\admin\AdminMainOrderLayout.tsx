import React from "react";
import { Outlet, useLocation, useNavigate } from "react-router-dom";
import { Calendar, Search, Download } from 'lucide-react';

const MainOrdersLayout: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  return (
    <div className="p-4 bg-gray-50">
       <div className="flex flex-col lg:flex-row items-center justify-center lg:justify-between flex-wrap gap-4 text-2xl border border-gray-300 py-1 px-3 mb-4 rounded-2xl bg-white">

        
        <h2 className="text-3xl  font-bold p-3">Orders</h2>
        
        <div className="flex items-center gap-3 flex-grow sm:flex-grow-0">
          <div className="relative flex-grow sm:w-64">
            <input 
              type="text" 
              placeholder="Start date → End date" 
              className="w-full border border-gray-200 bg-gray-50 rounded-md py-2 pl-3 pr-10 text-sm focus:outline-none focus:ring-2 focus:ring-orange-500"
            />
            <Calendar className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
          </div>
          
          <div className="relative flex-grow sm:w-64">
            <input 
              type="text" 
              placeholder="Search Order" 
              className="w-full border border-gray-200 bg-gray-50 rounded-md py-2 pl-3 pr-10 text-sm focus:outline-none focus:ring-2 focus:ring-orange-500"
            />
            <button className="absolute inset-y-0 right-0 flex items-center px-3 rounded-r-md">
              <Search size={18} className="text-gray-600" />
            </button>
          </div>
          
          <button className="flex items-center gap-2 bg-orange-50 text-orange-500 border border-orange-500 p-1 rounded-md hover:bg-orange-500 hover:text-white transition-colors duration-200">
           
            <span className="hidden md:inline text-[18px] ">Download CSV</span>
            <Download size={18} />
          </button>
        </div>
      </div>
      
      <div className="flex bg-orange-50 px-2 py-1 rounded-t-2xl  border border-gray-300">
        <button
          onClick={() => navigate("/admin/orders/all")}
          className={` rounded-2xl font-medium text-lg text-gray-800 py-2 px-4  cursor-pointer ${
            location.pathname.includes("all") || location.pathname.endsWith("/admin/orders")
              ? "bg-orange-500 text-white"
              : "bg-orange-50 text-black"
          }`}
        >
          All Orders
        </button>
        <button
          onClick={() => navigate("/admin/orders/completed")}
          className={`rounded-2xl font-medium text-lg text-gray-800 mx-4 py-2 px-4 cursor-pointer ${
            location.pathname.includes("completed")
              ? "bg-orange-500 text-white"
              : "bg-orange-50 text-black"
          }`}
        >
          Completed Orders
        </button>
      </div>
      <Outlet />
    </div>
  );
};

export default MainOrdersLayout;