import React from "react";
import CustomModal from "../../CustomModal";
import { format } from "date-fns";

interface RefundOrderDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  order: any;
}

const RefundOrderDetailsModal: React.FC<RefundOrderDetailsModalProps> = ({
  isOpen,
  onClose,
  order
}) => {
  if (!order) return null;

  // Helper functions to extract data from the order structure
  const getCustomerName = () => {
    if (order.customername) return order.customername;
    if (order.customerId?.FirstName && order.customerId?.LastName) {
      return `${order.customerId.FirstName} ${order.customerId.LastName}`;
    }
    if (order.orderId?.recieptId?.customer) return order.orderId.recieptId.customer;
    return "Walk-in Customer";
  };

  const getOperatorName = () => {
    if (order.orderId?.operator) return order.orderId.operator;
    // if (order.userId?.name) return order.userId.name;
    return "Admin";
  };

  const getOrderDate = () => {
    if (order.orderId?.orderDate) return format(new Date(order.orderId.orderDate), "MMM d, yyyy");
    if (order.createdAt) return format(new Date(order.createdAt), "MMM d, yyyy");
    return "-";
  };

  const getRefundDate = () => {
    if (order.updatedAt) return format(new Date(order.updatedAt), "MMM d, yyyy");
    return "-";
  };

  const getOriginalTotal = () => {
    const baseTotal = order.grandTotal || order.orderId?.orderValue || 0;
    return baseTotal + getRefundAmountWithTax();
  };

  const getRefundAmount = () => {
    // Calculate base refund amount based on refunded items
    if (order.refundData && Array.isArray(order.refundData)) {
      return order.refundData.reduce((sum: number, item: any) => {
        return sum + (item.price * item.quantity);
      }, 0);
    }
    return 0;
  };

  const getTaxAmount = () => {
    if (order.lineValueTax) return order.lineValueTax;
    if (order.taxValue) return order.taxValue;
    if (order.tax && Array.isArray(order.tax)) {
      return order.tax.reduce((sum: number, t: any) => sum + (t.addtax || 0), 0);
    }
    return 0;
  };
  console.log(getTaxAmount)
  const getRefundAmountWithTax = () => {
    const baseRefundAmount = getRefundAmount();
    const taxRate = 0.30; // 30% tax
    const taxAmount = baseRefundAmount * taxRate;
    return baseRefundAmount + taxAmount; // Base amount + 30% tax
  };

  const getFinalPayableTotal = () => {
    const originalTotal = getOriginalTotal();
    const refundWithTax = getRefundAmountWithTax();
    return originalTotal - refundWithTax;
  };

  const getRefundedItems = () => {
    return order.refundData || [];
  };

  const getTotalRefundItems = () => {
    return getRefundedItems().length;
  };

  const getRefundReason = () => {
    // Look for refund reason in various places
    if (order.refundReason) return order.refundReason;
    if (order.reason) return order.reason;
    return "this is test reason for testing";
  };

  const formatCurrency = (amount: number) => {
    return `$${amount.toFixed(2)}`;
  };

  return (
    <CustomModal
      isOpen={isOpen}
      onClose={onClose}
      title={`Order Details - ${order.OrderNumber || order.orderNo || 'N/A'}`}
      width="max-w-4xl"
      zIndex={60}
    >
      <div className="p-6">
        <div className="grid grid-cols-2 gap-8">
          {/* Customer Information */}
          <div className="border-l-4 border-orange-500 pl-4">
            <h3 className="text-lg font-semibold text-orange-500 mb-4">Customer Information</h3>

            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Customer:</span>
                <span className="font-medium">{getCustomerName()}</span>
              </div>

              <div className="flex justify-between">
                <span className="text-gray-600">Operator:</span>
                <span className="font-medium">{getOperatorName()}</span>
              </div>

              <div className="flex justify-between">
                <span className="text-gray-600">Order Date:</span>
                <span className="font-medium">{getOrderDate()}</span>
              </div>

              <div className="flex justify-between">
                <span className="text-gray-600">Refund Date:</span>
                <span className="font-medium">{getRefundDate()}</span>
              </div>
            </div>
          </div>

          {/* Financial Summary */}
          <div className="border-l-4 border-orange-500 pl-4">
            <h3 className="text-lg font-semibold text-orange-500 mb-4">Financial Summary</h3>

            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Status:</span>
                <span className="font-medium text-blue-600">in progress</span>
              </div>

              <div className="flex justify-between">
                <span className="text-gray-600">Original Total (Before Refund):</span>
                <span className="font-medium">{formatCurrency(getOriginalTotal())}</span>
              </div>

              <div className="flex justify-between">
                <span className="text-gray-600">Refund Amount (Incl. 30% Tax):</span>
                <span className="font-medium text-red-600">-{formatCurrency(getRefundAmountWithTax())}</span>
              </div>

              <div className="flex justify-between border-t pt-2">
                <span className="text-gray-600 font-semibold">Final Payable Total:</span>
                <span className="font-bold text-green-600">{formatCurrency(getFinalPayableTotal())}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Refunded Items Section */}
        <div className="mt-8 border-l-4 border-orange-500 pl-4">
          <h3 className="text-lg font-semibold text-orange-500 mb-4">Refunded Items</h3>

          <div className="space-y-3 mb-4">
            <div className="flex justify-between">
              <span className="text-gray-600">Total Refund Items:</span>
              <span className="font-medium">{getTotalRefundItems()}</span>
            </div>

            <div className="flex justify-between">
              <span className="text-gray-600">Reason:</span>
              <span className="font-medium">{getRefundReason()}</span>
            </div>
          </div>

          {/* Items Table */}
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b-2 border-gray-200">
                  <th className="text-left py-3 px-4 font-semibold text-gray-700">Item</th>
                  <th className="text-center py-3 px-4 font-semibold text-gray-700">Price</th>
                  <th className="text-center py-3 px-4 font-semibold text-gray-700">Qty</th>
                  <th className="text-right py-3 px-4 font-semibold text-gray-700">Total</th>
                </tr>
              </thead>
              <tbody>
                {getRefundedItems().map((item: any, index: number) => (
                  <tr key={index} className="border-b border-gray-100">
                    <td className="py-3 px-4">{item.name || 'Unknown Item'}</td>
                    <td className="py-3 px-4 text-center">{formatCurrency(item.price || 0)}</td>
                    <td className="py-3 px-4 text-center text-blue-600 font-medium">{item.quantity || 1}</td>
                    <td className="py-3 px-4 text-right font-medium">{formatCurrency((item.price || 0) * (item.quantity || 1))}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </CustomModal>
  );
};

export default RefundOrderDetailsModal;
