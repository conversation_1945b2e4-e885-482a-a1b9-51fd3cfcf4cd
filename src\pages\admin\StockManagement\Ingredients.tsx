import { useState, useEffect } from 'react';
import { Search, PlusCircle, Edit, Package, Trash2, ChevronLeft, ChevronRight } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import {
  useGetIngredientsQuery,
  useDeleteIngredientMutation,
} from "../../../store/api/ingredientsApi";
import Swal from 'sweetalert2';

const Ingredients = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(5);

  const userId = localStorage.getItem('userId') || '';

  const { data: ingredientsData, isLoading, error, refetch } = useGetIngredientsQuery(userId);
  const [deleteIngredient, { isLoading: isDeleting }] = useDeleteIngredientMutation();

  const formatDate = (dateString: any) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Filter ingredients based on search term
  const filteredIngredients = ingredientsData?.filter(ingredient =>
    ingredient.IngredientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    ingredient.IngredientID.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentIngredients = filteredIngredients.slice(indexOfFirstItem, indexOfLastItem);

  const paginate = (pageNumber: any) => setCurrentPage(pageNumber);

  const handleDelete = (id: any) => {
    Swal.fire({
      title: 'Are you sure?',
      text: 'Do you want to delete this Ingredient?',
      icon: 'warning',
      showCancelButton: true,
          confirmButtonColor: '#3085d6',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'No, cancel!'
    }).then((result) => {
      if (result.isConfirmed) {
        confirmDelete(id);
      }
    });
  };

  const confirmDelete = async (id: any) => {
    try {
      await deleteIngredient(id).unwrap();
      Swal.fire({
        title: 'Deleted!',
        text: 'Ingredient deleted successfully',
        icon: 'success',
        timer: 2000,
        showConfirmButton: false
      });
      refetch();
    } catch (error) {
      console.error('Failed to delete ingredient:', error);
      Swal.fire({
        title: 'Error!',
        text: 'Failed to delete ingredient',
        icon: 'error',
        timer: 2000,
        showConfirmButton: false
      });
    }
  };

  const handleEdit = (id: any) => {
    navigate(`/admin/stock-management/ingredients/ingredients-form/${id}`);
  };

  const handleAddStock = (id: any) => { 
    navigate('/admin/stock-management/ingredients/stock-form', { state: { ingredientId: id } });
  };

  useEffect(() => {
    if (error) {
      Swal.fire({
        title: 'Error!',
        text: 'Failed to fetch ingredients',
        icon: 'error',
        timer: 2000,
        showConfirmButton: false
      });
      console.error('Error fetching ingredients:', error);
    }
  }, [error]);

  return (
    <div className="p-4 bg-gray-50">
      <div className="flex items-center justify-between md:flex-row flex-col mb-4 p-2 rounded-2xl border border-gray-200 bg-white">
        <h1 className="text-3xl p-3 font-medium">Ingredients</h1>
        
        <div className="flex items-center sm:flex-row flex-col-reverse gap-2">
          <div className="relative">
            <input
              type="text"
              placeholder="Search Ingredient"
              className="pl-10 pr-4 py-2 border border-gray-300 bg-gray-50 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          </div>
          
          <button 
            onClick={() => navigate('/admin/stock-management/ingredients/ingredients-form')}
            className="flex items-center gap-1 bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md"
          >
            <PlusCircle className="w-4 h-4" />
            <h1 className='text-md font-bold'>Add Ingredient</h1> 
          </button>
        </div>
      </div>
      
      {isLoading ? (
        <div className="bg-gray-50 p-4 min-h-screen flex justify-center items-center">
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-orange-500"></div>
            <span>Loading ingredients...</span>
          </div>
        </div>
      ) : error ? (
        <p className="text-center text-red-500">Failed to load ingredients.</p>
      ) : (
        <>
          <div className="border border-gray-200 rounded-2xl overflow-x-auto bg-white">
            <table className="w-full">
              <thead className="bg-orange-50">
                <tr className="border-b border-b-gray-200">
                  <th className="text-left py-3 px-4 font-medium text-sm">ID</th>
                  <th className="text-left py-3 px-4 font-medium text-sm">Name</th>
                  <th className="text-left py-3 px-4 font-medium text-sm">UM</th>
                  <th className="text-left py-3 px-4 font-medium text-sm">Stock</th>
                  <th className="text-left py-3 px-4 font-medium text-sm">Expiry</th>
                  <th className="text-left py-3 px-4 font-medium text-sm">Last Order Date</th>
                  <th className="text-right py-3 px-4 font-medium text-sm">Actions</th>
                </tr>
              </thead>
              
              <tbody>
                {currentIngredients.map((item) => (
                  <tr 
                    key={item.id} 
                    className="border-b border-b-gray-200 hover:bg-orange-50 transition-colors text-gray-700"
                  >
                    <td className="py-3 px-4 text-sm font-medium">{item.IngredientID}</td>
                    <td className="py-3 px-4 text-sm font-medium">{item.IngredientName}</td>
                    <td className="py-3 px-4 text-sm">{item.UnitofMeasurement?.name || 'N/A'}</td>
                    <td className="py-3 px-4 text-sm">{item.CurrentStock}</td>
                    <td className="py-3 px-4 text-sm">{formatDate(item.Expiry)}</td>
                    <td className="py-3 px-4 text-sm">{formatDate(item.createdAt)}</td>
                    <td className="py-3 px-4 text-right">
                      <div className="flex justify-end gap-4">
                        <button 
                          className="text-blue-500"
                          onClick={() => handleEdit(item.id)}
                          disabled={isDeleting}
                        >
                          <Edit className="w-5 h-5" />
                        </button>
                        <button 
                          className="text-green-600"
                          onClick={() => handleAddStock(item.id)}
                          disabled={isDeleting}
                        >
                          <Package className="w-5 h-5" />
                        </button>
                        <button 
                          className={`text-red-500 ${isDeleting ? "cursor-not-allowed opacity-50" : ""}`}
                          onClick={() => handleDelete(item.id)}
                          disabled={isDeleting}
                        >
                          <Trash2 className="w-5 h-5" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
                
                {currentIngredients.length === 0 && (
                  <tr>
                    <td colSpan={7} className="py-4 px-4 text-center text-gray-500">
                      No ingredients found.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
          
          {/* Pagination */}
          {filteredIngredients.length > 0 && (
            <div className="flex items-center justify-start mt-4 gap-2">
              <button
                onClick={() => paginate(currentPage - 1)}
                disabled={currentPage === 1}
                className={`flex items-center justify-center w-8 h-8 rounded-md border ${
                  currentPage === 1
                    ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                    : "bg-white text-gray-700 hover:bg-gray-50"
                }`}
              >
                <ChevronLeft size={16} />
              </button>

              {Array.from(
                { length: Math.ceil(filteredIngredients.length / itemsPerPage) },
                (_, i) => i + 1
              ).map((number) => (
                <button
                  key={number}
                  onClick={() => paginate(number)}
                  className={`w-8 h-8 rounded-md flex items-center justify-center border ${
                    currentPage === number
                      ? "bg-orange-500 text-white"
                      : "bg-white text-gray-700 hover:bg-gray-50"
                  }`}
                >
                  {number}
                </button>
              ))}

              <button
                onClick={() => paginate(currentPage + 1)}
                disabled={indexOfLastItem >= filteredIngredients.length}
                className={`flex items-center justify-center w-8 h-8 rounded-md border ${
                  indexOfLastItem >= filteredIngredients.length
                    ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                    : "bg-white text-gray-700 hover:bg-gray-50"
                }`}
              >
                <ChevronRight size={16} />
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default Ingredients;