import React, { useState, useEffect } from "react";
import { ChevronLeft } from "lucide-react";
import { useNavigate, useParams } from "react-router-dom";
import {
  usePostUnitOfMeasurementMutation,
  usePutUnitOfMeasurementMutation,
  useGetUnitOfMeasurementQuery,
} from '../../../store/api/unitOfMeasurementApi';
import { toast } from 'react-toastify';

const UnitOfMeasurementForm: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEditMode = Boolean(id);
  const userId = localStorage.getItem('userId') || '';

  // RTK Query hooks
  const { data: unitOfMeasurement } = useGetUnitOfMeasurementQuery(id || '', { skip: !isEditMode });
  const [postUnitOfMeasurement, { isLoading: isCreating }] = usePostUnitOfMeasurementMutation();
  const [putUnitOfMeasurement, { isLoading: isUpdating }] = usePutUnitOfMeasurementMutation();

  // Form state
  const [formData, setFormData] = useState({
    name: "",
    code: "",
    visibility: false
  });

  // Populate form data if in edit mode
  useEffect(() => {
    if (isEditMode && unitOfMeasurement) {
      setFormData({
        name: unitOfMeasurement.name || '',
        code: unitOfMeasurement.code || '',
        visibility: unitOfMeasurement.active !== false
      });
    }
  }, [isEditMode, unitOfMeasurement]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const submitData = {
        name: formData.name,
        code: formData.code,
        active: formData.visibility,
        userId: userId
      };

      console.log("Submitting form data:", submitData);

      if (isEditMode && id) {
        // Update existing unit of measurement
        await putUnitOfMeasurement({
          id,
          formData: {
            ...submitData,
            _id: id,
          }
        }).unwrap();
        toast.success('Unit of measurement updated successfully!');
      } else {
        // Create new unit of measurement
        await postUnitOfMeasurement(submitData).unwrap();
        toast.success('Unit of measurement created successfully!');
      }
      navigate(-1); // Go back to the previous page
    } catch (error) {
      console.error(isEditMode ? 'Error updating unit of measurement:' : 'Error creating unit of measurement:', error);
      toast.error(isEditMode ? 'Failed to update unit of measurement.' : 'Failed to create unit of measurement.');
    }
  };

  const handleCancel = () => {
    navigate(-1); // Go back to the previous page
  };

  const isLoading = isCreating || isUpdating;

  return (
    <div className="p-4 bg-gray-50 min-h-screen">
      {/* Header with back button */}
      <div className="mb-4 border border-gray-200 p-2 rounded-2xl bg-white">
        <button 
          onClick={handleCancel}
          className="flex items-center text-lg font-semibold text-gray-800"
        >
          <ChevronLeft size={20} />
          {isEditMode ? 'Edit Unit of Measurement' : 'Add Unit of Measurement'}
        </button>
      </div>

      <form onSubmit={handleSubmit}>
        {/* Unit of Measurement Details Section */}
        <div className="mb-6 bg-white rounded-2xl border border-gray-200">
          <h2 className="text-lg font-semibold text-gray-800 mb-4 p-2 bg-orange-50 rounded-t-2xl">Unit of Measurement Details</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 p-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Name
                <span className="text-red-500 ml-1">*</span>
              </label>
              <input
                type="text"
                name="name"
                placeholder="Enter Name"
                value={formData.name}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Code
                <span className="text-red-500 ml-1">*</span>
              </label>
              <input
                type="number"
                name="code"
                placeholder="Enter Code"
                value={formData.code}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                required
              />
            </div>
          </div>
        </div>

        {/* Permissions Section */}
        <div className="mb-6 bg-white border border-gray-200 rounded-2xl">
          <h2 className="text-lg font-semibold text-gray-800 mb-4 bg-orange-50 rounded-t-2xl p-2">Permissions</h2>
          
          <div className="flex items-center p-4">
            <label className="inline-flex items-center cursor-pointer">
              <div className={`relative w-11 h-6 rounded-full transition-all duration-200 ease-in-out ${formData.visibility ? 'bg-orange-500' : 'bg-gray-200'}`}>
                <input
                  type="checkbox"
                  name="visibility"
                  className="opacity-0 w-0 h-0"
                  checked={formData.visibility}
                  onChange={handleChange}
                />
                <span className={`absolute left-0.5 top-0.5 bg-white w-4 h-4 rounded-full transition-all duration-200 ease-in-out ${formData.visibility ? 'transform translate-x-5' : ''}`}></span>
              </div>
              <span className="ml-2 text-sm font-medium text-gray-700">Visibility</span>
            </label>
          </div>
        </div>

        {/* Button Group */}
        <div className="flex justify-end gap-4">
          <button
            type="button"
            onClick={handleCancel}
            className="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
          >
            Cancel
          </button>
          
          <button
            type="submit"
            disabled={isLoading}
            className={`px-4 py-2 rounded-md text-white ${
              isLoading ? 'bg-orange-300 cursor-not-allowed' : 'bg-orange-500 hover:bg-orange-600'
            }`}
          >
            {isLoading
              ? isEditMode ? 'Updating...' : 'Adding...'
              : isEditMode ? 'Update Unit of Measurement' : 'Add Unit of Measurement'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default UnitOfMeasurementForm;