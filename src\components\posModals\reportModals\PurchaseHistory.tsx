
import React, { useState, useRef, useEffect, useMemo } from "react";
import CustomModal from "../../CustomModal";
import { FiSearch } from "react-icons/fi";
import { DateRangePicker, type RangeKeyDict } from "react-date-range";
import "react-date-range/dist/styles.css"; // main style file
import "react-date-range/dist/theme/default.css"; // theme css file
import { format, isWithinInterval, parseISO } from "date-fns"; // For formatting dates and date operations
import OrderDetailsModal from "../OrderDetailsModal";
import { useGetCustomerOrderItemsQuery } from "../../../store/api/pos/customer";

interface PurchaseHistoryProps {
  isOpen: boolean;
  onClose: () => void;
}

interface PurchaseRecord {
  receiptNumber: string;
  operatorName: string;
  customer: string;
  points: number;
  recordDate: string;
  tax: number;
  amount: number;
  receivedAmount: number;
  dueAmount: number;
  date: string;
}

const Loader = () => {
  return (
    <div className="flex justify-center py-8">
      <div className="h-10 w-10 animate-spin rounded-full border-4 border-transparent border-t-orange-500 border-r-orange-500"></div>
    </div>
  );
};

const PurchaseHistory: React.FC<PurchaseHistoryProps> = ({
  isOpen,
  onClose,
}) => {
  const [purchaseRecords, setPurchaseRecords] = useState<PurchaseRecord[]>([]);
  const userId = localStorage.getItem("userId") || "";
  const { data, isLoading } = useGetCustomerOrderItemsQuery(userId);
  const [orderdetailid, setorderdetailid] = useState<any>(null);

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const recordsPerPage = 5;

  // Use useEffect to update purchaseRecords when data changes
  useEffect(() => {
    if (data) {
      setPurchaseRecords(data);
      console.log("the purchase record is ", purchaseRecords);
    }
  }, [data]);

  // Initialize date range with null values
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([
    null,
    null,
  ]);
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);

  const [searchTerm, setSearchTerm] = useState("");
  const calendarRef = useRef<HTMLDivElement>(null);
  const [startDate, endDate] = dateRange;

  const [isOrderDetailsOpen, setIsOrderDetailsOpen] = useState(false);

  // Filter records based on search term and date range
  const filteredRecords = useMemo(() => {
    let filtered = purchaseRecords;

    // Filter by search term
    if (searchTerm.trim()) {
      const lowerCaseSearchTerm = searchTerm.toLowerCase().trim();
      filtered = filtered.filter(record =>
        record.customer.toLowerCase().includes(lowerCaseSearchTerm)
      );
    }

    // Filter by date range if both dates are selected
    if (startDate && endDate) {
      // Ensure endDate includes the entire day by setting it to the end of the day
      const endOfDay = new Date(endDate);
      endOfDay.setHours(23, 59, 59, 999);

      filtered = filtered.filter(record => {
        const recordDate = parseISO(record.recordDate);
        return isWithinInterval(recordDate, { start: startDate, end: endOfDay });
      });
    }

    return filtered;
  }, [purchaseRecords, searchTerm, startDate, endDate]);

  // Get current records for pagination
  const indexOfLastRecord = currentPage * recordsPerPage;
  const indexOfFirstRecord = indexOfLastRecord - recordsPerPage;
  const currentRecords = filteredRecords.slice(indexOfFirstRecord, indexOfLastRecord);

  // Calculate total pages
  const totalPages = Math.ceil(filteredRecords.length / recordsPerPage);

  function formatDate(dateString: string) {
    const date = new Date(dateString);

    const day = String(date.getDate()).padStart(2, '0'); // Ensures 2-digit day
    const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-indexed
    const year = date.getFullYear();

    return `${day}/${month}/${year}`;
  }

  // Handle clicks outside of calendar to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        calendarRef.current &&
        !calendarRef.current.contains(event.target as Node)
      ) {
        setIsCalendarOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleDateRangeChange = (ranges: RangeKeyDict) => {
    const selection = ranges.selection;
    if (selection) {
      setDateRange([selection.startDate || null, selection.endDate || null]);
      // Reset to first page when date range changes
      setCurrentPage(1);
    }
  };

  const formatDateRange = () => {
    if (!startDate || !endDate) return "Select Date Range";
    return `${format(startDate, "MMM dd, yyyy")} → ${format(
      endDate,
      "MMM dd, yyyy"
    )}`;
  };

  // Clear date filter
  const clearDateFilter = () => {
    setDateRange([null, null]);
    setCurrentPage(1);
  };

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    // Reset to first page when searching
    setCurrentPage(1);
  };

  // Pagination handlers
  const goToNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const goToPreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const footer = (
    <div className="flex justify-between items-center p-2">
      <div className="flex items-center gap-2 border-1 border-gray-300 rounded-2xl">
        <button
          className={`px-4 py-2 text-sm font-medium ${currentPage === 1 ? 'text-gray-300 cursor-not-allowed' : 'text-[#9C9C9C] cursor-pointer'}`}
          onClick={goToPreviousPage}
          disabled={currentPage === 1}
        >
          ← Previous
        </button>
        <span className="w-12 text-center text-black border-x border-gray-300 rounded-lg p-2">
          {currentPage}
        </span>
        <button
          className={`px-4 py-2 text-sm font-medium ${currentPage >= totalPages ? 'text-gray-300 cursor-not-allowed' : 'text-[#9C9C9C] cursor-pointer'}`}
          onClick={goToNextPage}
          disabled={currentPage >= totalPages || filteredRecords.length === 0}
        >
          Next →
        </button>
      </div>
      <button
        onClick={onClose}
        className="px-14 py-2 border border-orange text-orange text-lg font-poppins font-semibold rounded-full cursor-pointer transition-colors"
      >
        Cancel
      </button>
    </div>
  );

  return (
    <CustomModal
      isOpen={isOpen}
      onClose={onClose}
      title="Purchase History"
      width="max-w-7xl"
      footer={footer}
    >
      <div className="p-6">
        {/* Search and Date Filter */}
        <div className="flex justify-between items-center mb-6 pb-3 border-b border-[#E4E4E4]">
          <div className="relative flex-1 mr-8">
            <div className="flex items-center">
              <FiSearch className="text-gray-400 mr-2" size={20} />
              <input
                type="text"
                placeholder="Search Customer"
                className="w-full py-2 focus:outline-none text-base"
                value={searchTerm}
                onChange={handleSearchChange}
              />
            </div>
            <div className="absolute right-0 top-0 bottom-0 border-r border-[#E4E4E4]"></div>
          </div>
          <div className="relative mx-15 flex items-center">
            <button
              onClick={() => setIsCalendarOpen(!isCalendarOpen)}
              className="border border-[#E4E4E4] rounded-2xl py-2 px-3 text-md cursor-pointer"
            >
              {formatDateRange()}
            </button>
            {startDate && endDate && (
              <button
                onClick={clearDateFilter}
                className="ml-2 text-orange hover:text-orange-700 text-sm"
              >
                Clear
              </button>
            )}
            {isCalendarOpen && (
              <div
                ref={calendarRef}
                className="absolute right-0 top-12 z-50 bg-white shadow-lg rounded-lg border border-gray-200"
              >
                <DateRangePicker
                  ranges={[
                    {
                      startDate: startDate || new Date(),
                      endDate: endDate || new Date(),
                      key: "selection",
                    },
                  ]}
                  onChange={handleDateRangeChange}
                  direction="horizontal"
                  moveRangeOnFirstSelection={false}
                />
              </div>
            )}
          </div>
        </div>

        {/* Active filters display */}
        {(searchTerm || (startDate && endDate)) && (
          <div className="mb-4 flex flex-wrap gap-2">
            <span className="text-sm text-gray-500">Active filters:</span>
            {searchTerm && (
              <span className="bg-gray-100 text-gray-700 text-sm px-3 py-1 rounded-full">
                Customer: {searchTerm}
              </span>
            )}
            {startDate && endDate && (
              <span className="bg-gray-100 text-gray-700 text-sm px-3 py-1 rounded-full">
                Date: {format(startDate, "dd/MM/yyyy")} - {format(endDate, "dd/MM/yyyy")}
              </span>
            )}
          </div>
        )}

        {/* Table with Loading State */}
        <div className="overflow-x-auto">
          {isLoading ? (
            <Loader />
          ) : (
            <table className="w-full">
              <thead>
                <tr className="text-left text-[#9C9C9C] text-xs font-extralight border-b border-[#E4E4E4]">
                  <th className="pb-3">Receipt Number</th>
                  <th className="pb-3">Operator Name</th>
                  <th className="pb-3">Customer</th>
                  <th className="pb-3">Points</th>
                  <th className="pb-3">Record Date</th>
                  <th className="pb-3">Tax</th>
                  <th className="pb-3">Amount</th>
                  <th className="pb-3">Received Amount</th>
                  <th className="pb-3">Due Amount</th>
                  <th className="pb-3">Date</th>
                  <th className="pb-3">Actions</th>
                </tr>
              </thead>
              <tbody>
                {currentRecords.length > 0 ? (
                  currentRecords.map((record, index) => (
                    <tr key={index} className="text-sm border-b border-[#E4E4E4]">
                      <td className="py-4">{record.receiptNumber}</td>
                      <td className="py-4">{record.operatorName}</td>
                      <td className="py-4">{record.customer}</td>
                      <td className="py-4">{record.points}</td>
                      <td className="py-4">{formatDate(record.recordDate)}</td>
                      <td className="py-4">${record.tax.toFixed(2)}</td>
                      <td className="py-4">${record.amount.toFixed(2)}</td>
                      <td className="py-4">${record.receivedAmount.toFixed(2)}</td>
                      <td className="py-4">${record.dueAmount.toFixed(2)}</td>
                      <td className="py-4">{formatDate(record.date)}</td>
                      <td className="py-4">
                        <button
                          className="bg-orange text-white px-5 py-2 rounded-full text-sm font-semibold cursor-pointer"
                          onClick={() => {
                            setorderdetailid(record);

                            console.log(orderdetailid);
                            setIsOrderDetailsOpen(true);
                          }}
                        >
                          View Receipt
                        </button>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={11} className="py-4 text-center text-gray-500">
                      {searchTerm || (startDate && endDate)
                        ? `No records found matching your filters`
                        : "No records available"}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          )}
        </div>

        {/* Pagination info */}
        {filteredRecords.length > 0 && (
          <div className="mt-4 text-sm text-gray-500 text-right">
            Showing {indexOfFirstRecord + 1}-{Math.min(indexOfLastRecord, filteredRecords.length)} of {filteredRecords.length} records
          </div>
        )}
      </div>
      {isOrderDetailsOpen && (
        <OrderDetailsModal
          isOpen={isOrderDetailsOpen}
          order={orderdetailid}
          onClose={() => setIsOrderDetailsOpen(false)}
        />
      )}
    </CustomModal>
  );
};

export default PurchaseHistory;
