import React, { useState } from 'react';
import { ChevronLeft, Edit, Trash2, Save, X } from 'lucide-react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  useGetIngredientQuery,
  usePutIngredientMutation,
} from "../../../store/api/ingredientsApi";
import Swal from 'sweetalert2';

interface StockHistoryEntry {
  stock: number;
  price: number;
  entryDate?: string;
  expiry: string;
  createdAt?: string;
  _id?: string;
}

const IngredientNewStock = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const ingredientId = location.state?.ingredientId as string || '';

  const [newStock, setNewStock] = useState('');
  const [newPrice, setNewPrice] = useState('');
  const [entryDate, setEntryDate] = useState('');
  const [newExpiryDate, setNewExpiryDate] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [editFormData, setEditFormData] = useState<{
    stock: string;
    price: string;
    entryDate: string;
    expiry: string;
  }>({
    stock: '',
    price: '',
    entryDate: '',
    expiry: '',
  });
  
  const [userModifiedDate, setUserModifiedDate] = useState(false);
  const { data: ingredientData, isLoading, error } = useGetIngredientQuery(ingredientId, {
    skip: !ingredientId,
  });
  const [putIngredient] = usePutIngredientMutation();
  const formatDate = (dateString: string | number | Date | undefined) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString as string);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatDateForInput = (dateString: string | number | Date | undefined) => {
    if (!dateString) return '';
    const date = new Date(dateString as string);
    return date.toISOString().split('T')[0];
  };

  const updateIngredient = async (updatedData: any) => {
    try {
      await putIngredient({
        id: ingredientId,
        formData: updatedData
      }).unwrap();
      return true;
    } catch (error) {
      console.error('Failed to update ingredient:', error);
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Failed to update ingredient. Please try again.',
        confirmButtonColor: '#f97316',
      });
      return false;
    }
  };
  const handleAddStock = async () => {
    if (!newStock || !newPrice || !entryDate || !newExpiryDate) {
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Please fill in all required fields',
        confirmButtonColor: '#f97316',
      });
      return;
    }
    setIsSubmitting(true);
    try {
      const stockHistoryEntry = {
        stock: parseFloat(newStock),
        price: parseFloat(newPrice),
        createdAt: entryDate,
        expiry: newExpiryDate,
      };

      const mongoId = ingredientData?._id || ingredientData?.id || '';
      const currentStockHistory = ingredientData?.stockHistory || [];
      const updatedStockHistory = [...currentStockHistory, stockHistoryEntry];

      const updatedData = {
        _id: mongoId,
        ...ingredientData,
        UnitPrice: parseFloat(newPrice),
        Expiry: newExpiryDate,
        stockHistory: updatedStockHistory
      };

      if (updatedData.id && updatedData._id) {
        delete updatedData.id;
      }

      const success = await updateIngredient(updatedData);

      if (success) {
        await Swal.fire({
          icon: 'success',
          title: 'Success',
          text: 'Stock added successfully',
          confirmButtonColor: '#f97316',
        });

      
        setNewStock('');
        setNewPrice('');
        setEntryDate('');
        setNewExpiryDate('');
        
        setUserModifiedDate(false); 
      }
    } catch (error) {
      console.error('Failed to add stock:', error);
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Failed to add stock. Please try again.',
        confirmButtonColor: '#f97316',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    navigate('/admin/stock-management/ingredients');
  };

  const handleDeleteStockEntry = async (index: number) => {
    if (!ingredientData || !ingredientData.stockHistory) return;

    const result = await Swal.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#f97316',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Yes, delete it!'
    });

    if (!result.isConfirmed) return;

    try {
      setIsSubmitting(true);

      const updatedStockHistory = [...ingredientData.stockHistory];
      updatedStockHistory.splice(index, 1);

      const updatedData = {
        ...ingredientData,
        CurrentStock: ingredientData.CurrentStock,
        UnitPrice: ingredientData.UnitPrice,
        Expiry: ingredientData.Expiry,
        stockHistory: updatedStockHistory
      };

      const success = await updateIngredient(updatedData);

      if (success) {
        await Swal.fire({
          icon: 'success',
          title: 'Deleted!',
          text: 'Stock history entry deleted successfully.',
          confirmButtonColor: '#f97316',
        });
      }
    } catch (error) {
      console.error('Failed to delete stock history entry:', error);
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Failed to delete stock history entry. Please try again.',
        confirmButtonColor: '#f97316',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditStockEntry = (index: number) => {
    if (!ingredientData || !ingredientData.stockHistory) return;

    const entry = ingredientData.stockHistory[index];
    if (!entry) return;

    setEditingIndex(index);
    setEditFormData({
      stock: entry.stock.toString(),
      price: entry.price.toString(),
      entryDate: formatDateForInput(entry.entryDate || entry.createdAt),
      expiry: formatDateForInput(entry.expiry),
    });
  };

  const handleCancelEdit = () => {
    setEditingIndex(null);
    setEditFormData({
      stock: '',
      price: '',
      entryDate: '',
      expiry: '',
    });
  };

  const handleUpdateStockEntry = async () => {
    if (editingIndex === null || !ingredientData || !ingredientData.stockHistory) return;

    try {
      setIsSubmitting(true);

      const updatedStockHistory = [...ingredientData.stockHistory];
      updatedStockHistory[editingIndex] = {
        ...updatedStockHistory[editingIndex],
        stock: parseFloat(editFormData.stock),
        price: parseFloat(editFormData.price),
        createdAt: editFormData.entryDate,
        expiry: editFormData.expiry
      };

      const latestEntry = updatedStockHistory[updatedStockHistory.length - 1];
      const updatedData = {
        ...ingredientData,
        UnitPrice: latestEntry.price,
        Expiry: latestEntry.expiry,
        stockHistory: updatedStockHistory
      };

      const success = await updateIngredient(updatedData);

      if (success) {
        await Swal.fire({
          icon: 'success',
          title: 'Success',
          text: 'Stock entry updated successfully',
          confirmButtonColor: '#f97316',
        });
        setEditingIndex(null);
        setEditFormData({
          stock: '',
          price: '',
          entryDate: '',
          expiry: '',
        });
      }
    } catch (error) {
      console.error('Failed to update stock entry:', error);
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Failed to update stock entry. Please try again.',
        confirmButtonColor: '#f97316',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditFormChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setEditFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handler for the entry date change
  const handleEntryDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEntryDate(e.target.value);
    setUserModifiedDate(true); // Mark that user explicitly modified the date
  };

  if (isLoading) {

    return <div className="bg-gray-50 p-4 min-h-screen flex justify-center items-center">
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-orange-500"></div>
            <span>Loading ingredient details...</span>
          </div>
        </div>
  }

  if (error || !ingredientData) {
    return (
      <div className="p-4 text-center text-red-500">
        Error loading ingredient details. Please try again.
      </div>
    );
  }

  const stockHistory = ingredientData?.stockHistory || [];

  return (
    <div className="p-4 bg-gray-50">
      {/* Header */}
      <div className="flex items-center mb-6 bg-white rounded-2xl p-2 border border-gray-200">
        <ChevronLeft
          onClick={handleCancel}
          className="w-5 h-5 mr-2 cursor-pointer"
        />
        <h1 className="text-xl font-medium">Add New Stock</h1>
      </div>

      {/* Ingredient Details Section */}
      <div className="border mb-6 border-gray-200 rounded-2xl">
        <div className="bg-orange-50 rounded-t-2xl p-2">
          <h2 className="text-lg font-medium">Ingredient Details</h2>
        </div>

        <div className="bg-white rounded-b-2xl shadow-sm">
          <table className="w-full">
            <thead>
              <tr className="border-b border-b-gray-200">
                <th className="text-left py-3 px-4 font-medium text-sm">ID</th>
                <th className="text-left py-3 px-4 font-medium text-sm">Name</th>
                <th className="text-left py-3 px-4 font-medium text-sm">UM</th>
                <th className="text-left py-3 px-4 font-medium text-sm">Current Stock</th>
                <th className="text-left py-3 px-4 font-medium text-sm">Current Expiry</th>
                <th className="text-left py-3 px-4 font-medium text-sm">Last Order Date</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="py-3 px-4 text-sm">{ingredientData.IngredientID}</td>
                <td className="py-3 px-4 text-sm">{ingredientData.IngredientName}</td>
                <td className="py-3 px-4 text-sm">{ingredientData.UnitofMeasurement?.name || 'N/A'}</td>
                <td className="py-3 px-4 text-sm">{ingredientData.CurrentStock}</td>
                <td className="py-3 px-4 text-sm">{formatDate(ingredientData.Expiry)}</td>
                <td className="py-3 px-4 text-sm">
                  {formatDate(ingredientData.createdAt)}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* Stock History Section */}
      <div className="border border-gray-200 rounded-2xl mb-6">
        <div className="bg-orange-50 rounded-t-2xl p-2">
          <h2 className="text-lg font-medium">Stock Details</h2>
        </div>

        <div className="bg-white rounded-b-2xl shadow-sm">
          <table className="w-full">
            <thead>
              <tr className="border-b border-b-gray-200">
                <th className="text-left py-3 px-4 font-medium text-sm">Stock</th>
                <th className="text-left py-3 px-4 font-medium text-sm">Price</th>
                <th className="text-left py-3 px-4 font-medium text-sm">Entry Date</th>
                <th className="text-left py-3 px-4 font-medium text-sm">Expiry Date</th>
                <th className="text-center py-3 px-4 font-medium text-sm">Actions</th>
              </tr>
            </thead>
            <tbody>
              {stockHistory && stockHistory.length > 0 ? (
                stockHistory.map((entry: StockHistoryEntry, index: number) => (
                  <React.Fragment key={entry._id || index}>
                    <tr className="border-b border-b-gray-100">
                      <td className="py-3 px-4 text-sm">{entry.stock}</td>
                      <td className="py-3 px-4 text-sm">${entry.price}</td>
                      <td className="py-3 px-4 text-sm">
                        {formatDate(entry.entryDate || entry.createdAt)}
                      </td>
                      <td className="py-3 px-4 text-sm">{formatDate(entry.expiry)}</td>
                      <td className="py-3 px-4 text-sm flex justify-center space-x-2">
                        <button
                          onClick={() => handleEditStockEntry(index)}
                          className="text-blue-500"
                          disabled={isSubmitting || editingIndex !== null}
                        >
                          <Edit className="w-5 h-5" />
                        </button>
                        <button
                          onClick={() => handleDeleteStockEntry(index)}
                          className="text-red-500"
                          disabled={isSubmitting || editingIndex !== null}
                          title="Delete only this history entry without affecting current stock"
                        >
                          <Trash2 className="w-5 h-5" />
                        </button>
                      </td>
                    </tr>
                    {editingIndex === index && (
                      <tr className="bg-orange-50 border-b border-b-gray-100">
                        <td colSpan={5} className="p-4">
                          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                              <label className="block text-sm text-gray-700 mb-1">
                                Stock <span className="text-red-500">*</span>
                              </label>
                              <input
                                type="number"
                                min="0"
                                step="0.01"
                                name="stock"
                                className="w-full border border-gray-300 rounded-md p-2 text-sm focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
                                value={editFormData.stock}
                                onChange={handleEditFormChange}
                              />
                            </div>

                            <div>
                              <label className="block text-sm text-gray-700 mb-1">
                                Price <span className="text-red-500">*</span>
                              </label>
                              <input
                                type="number"
                                min="0"
                                step="0.01"
                                name="price"
                                className="w-full border border-gray-300 rounded-md p-2 text-sm focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
                                value={editFormData.price}
                                onChange={handleEditFormChange}
                              />
                            </div>

                            <div>
                              <label className="block text-sm text-gray-700 mb-1">
                                Entry Date <span className="text-red-500">*</span>
                              </label>
                              <div className="relative">
                                <input
                                  type="date"
                                  name="entryDate"
                                  className="w-full border border-gray-300 rounded-md p-2 text-sm pr-4 focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
                                  value={editFormData.entryDate}
                                  onChange={handleEditFormChange}
                                />
                              </div>
                            </div>

                            <div>
                              <label className="block text-sm text-gray-700 mb-1">
                                Expiry Date <span className="text-red-500">*</span>
                              </label>
                              <div className="relative">
                                <input
                                  type="date"
                                  name="expiry"
                                  className="w-full border border-gray-300 rounded-md p-2 text-sm pr-4 focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
                                  value={editFormData.expiry}
                                  onChange={handleEditFormChange}
                                />
                              </div>
                            </div>
                          </div>

                          <div className="flex justify-end space-x-4 mt-4">
                            <button
                              className="flex items-center px-4 py-2 rounded-md border border-gray-300 text-sm"
                              onClick={handleCancelEdit}
                              disabled={isSubmitting}
                            >
                              <X className="w-4 h-4 mr-1" /> Cancel
                            </button>
                            <button
                              className={`flex items-center px-4 py-2 rounded-md bg-orange-500 text-white text-sm ${isSubmitting || !editFormData.stock || !editFormData.price || !editFormData.entryDate || !editFormData.expiry
                                ? "opacity-60 cursor-not-allowed"
                                : "hover:bg-orange-600"
                                }`}
                              onClick={handleUpdateStockEntry}
                              disabled={isSubmitting || !editFormData.stock || !editFormData.price || !editFormData.entryDate || !editFormData.expiry}
                            >
                              <Save className="w-4 h-4 mr-1" /> {isSubmitting ? "Updating..." : "Update"}
                            </button>
                          </div>
                        </td>
                      </tr>
                    )}
                  </React.Fragment>
                ))
              ) : (
                <tr>
                  <td colSpan={5} className="py-4 px-4 text-center text-gray-500">
                    No stock history available
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Add New Stock Section */}
      <div className="border border-gray-200 rounded-2xl">
        <div className="bg-orange-50 rounded-t-2xl p-2">
          <h2 className="text-lg font-medium">Add New Stock</h2>
        </div>

        <div className="bg-white rounded-b-2xl p-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm text-gray-700 mb-1">
                New Stock <span className="text-red-500">*</span>
              </label>
              <input
                type="number"
                min="0"
                step="0.01"
                placeholder="Enter New Stock"
                className="w-full border border-gray-300 rounded-md p-2 text-sm focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
                value={newStock}
                onChange={(e) => setNewStock(e.target.value)}
              />
            </div>

            <div>
              <label className="block text-sm text-gray-700 mb-1">
                New Price <span className="text-red-500">*</span>
              </label>
              <input
                type="number"
                min="0"
                step="0.01"
                placeholder="Enter New Price"
                className="w-full border border-gray-300 rounded-md p-2 text-sm focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
                value={newPrice}
                onChange={(e) => setNewPrice(e.target.value)}
              />
            </div>

            <div>
              <label className="block text-sm text-gray-700 mb-1">
                Entry Date <span className="text-red-500">*</span>
                {userModifiedDate && <span className="text-xs text-blue-500 ml-1">(modified)</span>}
              </label>
              <div className="relative">
                <input
                  type="date"
                  className="w-full border border-gray-300 rounded-md p-2 text-sm pr-4 focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
                  value={entryDate}
                  onChange={handleEntryDateChange}
                />
              </div>
            </div>

            <div>
              <label className="block text-sm text-gray-700 mb-1">
                New Expiry Date <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <input
                  type="date"
                  className="w-full border border-gray-300 rounded-md p-2 text-sm pr-4 focus:ring-1 focus:ring-orange-500 focus:border-orange-500"
                  value={newExpiryDate}
                  onChange={(e) => setNewExpiryDate(e.target.value)}
                />
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4 mt-6">
            <button
              className="px-6 py-2 rounded-md border border-gray-300 text-sm"
              onClick={handleCancel}
              disabled={isSubmitting || editingIndex !== null}
            >
              Cancel
            </button>
            <button
              className={`px-6 py-2 rounded-md bg-orange-500 hover:bg-orange-600 text-white text-sm "
                }`}
              onClick={handleAddStock}
              // disabled={isSubmitting || !newStock || !newPrice || !entryDate || !newExpiryDate || editingIndex !== null}
            >
              {isSubmitting ? "Adding..." : "Add Stock"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IngredientNewStock;