import React, { useEffect, useRef, useState } from "react";
import CustomModal from "../../CustomModal";
import { DateRangePicker, type RangeKeyDict } from "react-date-range";
import "react-date-range/dist/styles.css";
import "react-date-range/dist/theme/default.css";
import { format, isWithinInterval } from "date-fns";
import { FiSearch } from "react-icons/fi";
import { useGetCustomersQuery } from "../../../store/api/pos/customer";

interface NewMembersProps {
  isOpen: boolean;
  onClose: () => void;
}

interface Member {
  card: string;
  person: string;
  date: string;
  rawDate: Date | null; // Store the raw date for filtering
}

const NewMembers: React.FC<NewMembersProps> = ({ isOpen, onClose }) => {
  // Initialize date range with null values
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([
    null,
    null,
  ]);
  const userId = localStorage.getItem("userId")
  const { data, isLoading, error } = useGetCustomersQuery(userId || "")
  console.log(data)
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const calendarRef = useRef<HTMLDivElement>(null);
  const [startDate, endDate] = dateRange;
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5;

  // Handle clicks outside of calendar to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        calendarRef.current &&
        !calendarRef.current.contains(event.target as Node)
      ) {
        setIsCalendarOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleDateRangeChange = (ranges: RangeKeyDict) => {
    const selection = ranges.selection;
    if (selection) {
      setDateRange([selection.startDate || null, selection.endDate || null]);
      // Reset to first page when date range changes
      setCurrentPage(1);
      // Close calendar after selection
      setIsCalendarOpen(false);
    }
  };

  // Clear date filter
  const clearDateFilter = () => {
    setDateRange([null, null]);
    setCurrentPage(1);
  };

  const formatDateRange = () => {
    if (!startDate || !endDate) return "Select Date Range";
    return `${format(startDate, "MMM dd, yyyy ")} → ${format(
      endDate,
      "MMM dd, yyyy"
    )}`;
  };

  // Transform API data to Member format
  const members: Member[] = data ? data.map((customer: any) => {
    // Parse the date string to a Date object for filtering
    console.log(customer.CustomerLoyalty?.CardNo)
    let rawDate = null;
    if (customer.CustomerLoyalty?.StartDate) {
      try {
        rawDate = new Date(customer.CustomerLoyalty.StartDate);
      } catch (e) {
        console.error("Error parsing date:", e);
      }
    }

    return {
      card: customer.CustomerLoyalty?.CardNo || '0000-0000-0000',
      person: `${customer.FirstName || ''} ${customer.LastName || ''}`.trim(),
      date: rawDate ? format(rawDate, "MMM dd, yyyy") : 'N/A',
      rawDate: rawDate
    };
  }) : [];

  // Filter members based on search term and date range
  const filteredMembers = members.filter(member => {
    // First filter by search term
    const matchesSearch =
      member.person.toLowerCase().includes(searchTerm.toLowerCase()) ||
      member.card.toLowerCase().includes(searchTerm.toLowerCase());

    // Only filter by date range if both dates are selected and member has a valid date
    const matchesDateRange = !startDate || !endDate || !member.rawDate
      ? true // If any date is missing, don't filter by date
      : isWithinInterval(member.rawDate, {
        start: startDate,
        end: endDate
      });

    return matchesSearch && matchesDateRange;
  });

  // Calculate pagination
  const totalPages = Math.ceil(filteredMembers.length / itemsPerPage);
  const isFirstPage = currentPage === 1;
  const isLastPage = currentPage === totalPages || totalPages === 0;

  // Get current page items
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredMembers.slice(indexOfFirstItem, indexOfLastItem);

  // Reset to first page when search term changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  const handleLoad = () => {
    // Implement load functionality
  };

  const footer = (
    <div className="flex justify-between items-center p-2">
      <div className="flex items-center gap-2 border-1 border-gray-300 rounded-2xl">
        <button
          className={`px-4 py-2 text-sm font-medium ${isFirstPage ? 'text-gray-300 cursor-not-allowed' : 'text-[#9C9C9C] cursor-pointer'}`}
          onClick={() => !isFirstPage && setCurrentPage((prev) => Math.max(prev - 1, 1))}
          disabled={isFirstPage}
        >
          ← Previous
        </button>
        <span className="w-12 text-center text-black border-x border-gray-300 rounded-lg p-2">
          {currentPage}
        </span>
        <button
          className={`px-4 py-2 text-sm font-medium ${isLastPage ? 'text-gray-300 cursor-not-allowed' : 'text-[#9C9C9C] cursor-pointer'}`}
          onClick={() => !isLastPage && setCurrentPage((prev) => prev + 1)}
          disabled={isLastPage}
        >
          Next →
        </button>
      </div>
      <div className="flex gap-4">
        <button
          onClick={handleLoad}
          className="px-16 py-2 bg-orange text-white rounded-full cursor-pointer transition-colors"
        >
          Load
        </button>
        <button
          onClick={onClose}
          className="px-14 py-2 border border-orange text-orange text-lg font-poppins font-semibold rounded-full cursor-pointer transition-colors"
        >
          Cancel
        </button>
      </div>
    </div>
  );

  return (
    <CustomModal
      isOpen={isOpen}
      onClose={onClose}
      title="New Members"
      width="max-w-4xl"
      footer={footer}
    >
      <div className="p-6">
        {/* Search and Date Range */}
        <div className="flex justify-between items-center mb-6 pb-3 border-b border-[#E4E4E4]">
          <div className="relative flex-1 mr-8">
            <div className="flex items-center">
              <FiSearch className="text-gray-400 mr-2" size={20} />
              <input
                type="text"
                placeholder="Search Members"
                className="w-full py-2 focus:outline-none text-base"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="absolute right-0 top-0 bottom-0 border-r border-[#E4E4E4]"></div>
          </div>
          <div className="relative mx-15 flex items-center">
            <button
              onClick={() => setIsCalendarOpen(!isCalendarOpen)}
              className="border border-[#E4E4E4] rounded-2xl py-2 px-3 text-md cursor-pointer"
            >
              {formatDateRange()}
            </button>
            {startDate && endDate && (
              <button
                onClick={clearDateFilter}
                className="ml-2 text-orange hover:text-orange-700 text-sm"
              >
                Clear
              </button>
            )}
            {isCalendarOpen && (
              <div
                ref={calendarRef}
                className="absolute right-0 top-12 z-50 bg-white shadow-lg rounded-lg border border-gray-200"
              >
                <DateRangePicker
                  ranges={[
                    {
                      startDate: startDate || new Date(),
                      endDate: endDate || new Date(),
                      key: "selection",
                    },
                  ]}
                  onChange={handleDateRangeChange}
                  direction="horizontal"
                  moveRangeOnFirstSelection={false}
                />
              </div>
            )}
          </div>
        </div>

        {/* Active filters display */}
        {(searchTerm || (startDate && endDate)) && (
          <div className="mb-4 flex flex-wrap gap-2">
            <span className="text-sm text-gray-500">Active filters:</span>
            {searchTerm && (
              <span className="bg-gray-100 text-gray-700 text-sm px-3 py-1 rounded-full">
                Search: {searchTerm}
              </span>
            )}
            {startDate && endDate && (
              <span className="bg-gray-100 text-gray-700 text-sm px-3 py-1 rounded-full">
                Date: {format(startDate, "dd/MM/yyyy")} - {format(endDate, "dd/MM/yyyy")}
              </span>
            )}
          </div>
        )}

        {/* Members Table */}
        <div className="mb-6">
          <div className="grid grid-cols-3 gap-4 px-4 py-2 bg-gray-50 rounded-t-lg">
            <div className="text-gray-600 font-medium">Card</div>
            <div className="text-gray-600 font-medium">Person</div>
            <div className="text-gray-600 font-medium">Date</div>
          </div>
          <div className="divide-y divide-gray-100">
            {isLoading ? (
              <div className="flex justify-center py-8">
                <div className="h-10 w-10 animate-spin rounded-full border-4 border-transparent border-t-orange-500 border-r-orange-500"></div>
              </div>
            ) : error ? (
              <div className="text-center py-4 text-red-500">Error loading members</div>
            ) : currentItems.length === 0 ? (
              <div className="text-center py-4">
                {searchTerm || (startDate && endDate)
                  ? "No members found matching your filters"
                  : "No members available"}
              </div>
            ) : (
              currentItems.map((member, index) => (
                <div
                  key={index}
                  className="grid grid-cols-3 gap-4 px-4 py-3 hover:bg-gray-50"
                >
                  <div className="text-gray-800">{member.card}</div>
                  <div className="text-gray-800">{member.person}</div>
                  <div className="text-gray-800">{member.date}</div>
                </div>
              ))
            )}
          </div>
          {!isLoading && !error && filteredMembers.length > 0 && (
            <div className="text-right text-sm text-gray-500 mt-2 pr-4">
              Showing {indexOfFirstItem + 1}-{Math.min(indexOfLastItem, filteredMembers.length)} of {filteredMembers.length} members
            </div>
          )}
        </div>
      </div>
    </CustomModal>
  );
};

export default NewMembers;