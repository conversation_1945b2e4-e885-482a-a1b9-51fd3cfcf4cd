
import React from "react";
import CustomModal from "../../CustomModal";
import { FiEdit, FiTrash2, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ck } from "react-icons/fi";
import { useDeleteBillDenominationMutation, useUpdateBillDenominationMutation } from "../../../store/api/pos/customer";
import Swal from 'sweetalert2';

interface CashierBillDemonstrationModalProps {
  isOpen: boolean;
  onClose: () => void;
  data: {
    _id: string;
    EnteredBy: string;
    createdAt: string;
    denominations: Array<{
      value: number;
      quantity: number;
      _id: string;
    }>;
    totalCashOnhand: number;
    updatedAt: string;
    userId: string;
  } | null;
}

const CashierBillDemonstrationModal: React.FC<CashierBillDemonstrationModalProps> = ({
  isOpen,
  onClose,
  data
}) => {
  const [isEditMode, setIsEditMode] = React.useState(false);
  const [editedDenominations, setEditedDenominations] = React.useState(data?.denominations || []);
  let userId = localStorage.getItem("userId") || ""
  const [updateBillDenomination, { isLoading, }] = useUpdateBillDenominationMutation();
  const [deleteBillDenomination, { isLoading: isdeleteloading }] = useDeleteBillDenominationMutation();
  // Update edited denominations when data changes
  React.useEffect(() => {
    if (data) {
      setEditedDenominations(data.denominations);
    }
  }, [data]);

  if (!data) return null;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const calculateTotal = (value: number, quantity: number) => {
    return (value * quantity).toFixed(2);
  };

  // Calculate total cash from edited denominations
  const calculateTotalCash = () => {
    return editedDenominations.reduce((total, denom) => {
      return total + (denom.value * denom.quantity);
    }, 0);
  };

  // Handle quantity change
  const handleQuantityChange = (denominationId: string, newQuantity: string) => {
    const quantity = parseInt(newQuantity) || 0;
    setEditedDenominations(prev =>
      prev.map(denom =>
        denom._id === denominationId
          ? { ...denom, quantity }
          : denom
      )
    );
  };

  const handleEdit = () => {
    setIsEditMode(!isEditMode);
  };

  const handleDelete = async () => {
    const result = await Swal.fire({
      title: 'Are you sure?',
      text: 'Do you want to delete this bill denomination record? This action cannot be undone!',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'No, cancel!',
      focusConfirm: false,
      showLoaderOnConfirm: true,
      preConfirm: async () => {
        try {
          await deleteBillDenomination(data._id).unwrap();
          return true;
        } catch (error) {
          console.error("Error deleting bill denomination:", error);
          Swal.showValidationMessage('Failed to delete bill denomination. Please try again.');
          return false;
        }
      },
      allowOutsideClick: () => !Swal.isLoading()
    });

    if (result.isConfirmed) {
      // Show success message
      Swal.fire({
        icon: 'success',
        title: 'Deleted!',
        text: 'Bill denomination record has been deleted successfully.',
        confirmButtonColor: '#f97316', // Orange color to match theme
        timer: 2000,
        showConfirmButton: false,
      });

      // Close the modal after successful deletion
      onClose();
    }
  };

  const handlePrintReceipt = () => {
    console.log("Print Receipt clicked");
  };

  const handleDone = async () => {
    if (isEditMode) {
      try {
        // Prepare the update data according to the API payload structure
        const updateData = {
          EnteredBy: data.EnteredBy,
          denominations: editedDenominations,
          totalCashOnhand: calculateTotalCash(),
          userId: userId
        };

        // Call the API to update bill denomination
        await updateBillDenomination({ _id: data._id, data: updateData }).unwrap();

        // Show success message with SweetAlert
        Swal.fire({
          icon: 'success',
          title: 'Success!',
          text: 'Bill denomination updated successfully!',
          confirmButtonColor: '#f97316', // Orange color to match theme
          timer: 2000,
          showConfirmButton: false,
        });

        setIsEditMode(false);
      } catch (error) {
        console.error("Error updating bill denomination:", error);

        // Show error message with SweetAlert
        Swal.fire({
          icon: 'error',
          title: 'Error!',
          text: 'Failed to update bill denomination. Please try again.',
          confirmButtonColor: '#f97316', // Orange color to match theme
        });
      }
    }
  };

  const handleCancel = () => {
    // Reset to original data and exit edit mode
    if (data) {
      setEditedDenominations(data.denominations);
    }
    setIsEditMode(false);
    onClose();
  };

  return (
    <CustomModal
      isOpen={isOpen}
      onClose={onClose}
      title="Cashier Bill Demonstration Report"
      width="max-w-7xl"
    >
      <div className="p-6 h-[600px]">
        <div className="flex gap-6 h-full">
          {/* Left - Denominations Table (Scrollable) */}
          <div className="flex-1 flex flex-col h-full">
            <div className="grid grid-cols-3 gap-4 text-sm font-medium text-gray-600 border-b pb-2 mb-4">
              <div>Denomination</div>
              <div className="text-center">Quantity</div>
              <div className="text-right">Total</div>
            </div>

            {/* Scrollable content area */}
            <div className="flex-1 overflow-y-auto pr-2">
              <div className="space-y-4">
                {editedDenominations.map((denomination) => (
                  <div key={denomination._id} className="grid grid-cols-3 gap-4 items-center">
                    <div className="bg-gray-100 rounded px-3 py-2 text-sm font-medium">
                      ${denomination.value.toFixed(2)}
                    </div>
                    <input
                      type="number"
                      value={denomination.quantity}
                      onChange={(e) => handleQuantityChange(denomination._id, e.target.value)}
                      disabled={!isEditMode}
                      className={`text-center w-full border rounded px-3 py-2 text-sm focus:outline-none ${isEditMode
                        ? 'bg-white border-orange-300 focus:border-orange-500'
                        : 'bg-gray-100 border-gray-300 cursor-not-allowed'
                        }`}
                    />
                    <div className="text-right text-sm font-medium">
                      ${calculateTotal(denomination.value, denomination.quantity)}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right - Details and Actions (Fixed) */}
          <div className="w-80 flex flex-col h-full">
            <div className="bg-gray-50 rounded-lg p-4 shadow-sm">
              <h3 className="text-lg font-semibold mb-4 text-gray-800">Details</h3>

              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Date of Entry</span>
                  <span className="font-medium">{formatDate(data.createdAt)}</span>
                </div>

                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Updated At</span>
                  <span className="font-medium">{formatDate(data.updatedAt)}</span>
                </div>

                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Entered By</span>
                  <span className="font-medium">{data.EnteredBy}</span>
                </div>

                <div className="border-t pt-3 mt-4">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600 text-sm">Total Cash</span>
                    <span className="font-bold text-orange-600 text-lg">
                      ${calculateTotalCash().toFixed(2)}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="mt-6 grid grid-cols-2 gap-3">
              <button
                onClick={handleEdit}
                className="flex hover:bg-orange items-center cursor-pointer flex-col justify-center gap-1 bg-gray-900 text-white px-4 py-3 rounded-lg text-sm  transition-colors"
              >
                <FiEdit size={18} />
                Edit
              </button>

              <button
                onClick={handleDelete}
                disabled={isdeleteloading}
                className={`flex items-center cursor-pointer flex-col hover:bg-orange justify-center gap-1 px-4 py-3 rounded-lg text-sm transition-colors ${!isdeleteloading
                  ? 'bg-gray-900 text-white hover:bg-gray-700'
                  : 'bg-gray-400 text-gray-200 cursor-not-allowed'
                  }`}
              >
                <FiTrash2 size={18} />
                {isdeleteloading ? 'Deleting...' : 'Delete'}
              </button>

              <button
                onClick={handlePrintReceipt}
                className="flex items-center  cursor-pointer flex-col justify-center gap-1 bg-gray-900 text-white px-4 py-3 rounded-lg text-sm hover:bg-orange transition-colors"
              >
                <FiPrinter size={18} />
                Print Receipt
              </button>

              <button
                onClick={handleDone}
                disabled={!isEditMode || isLoading}
                className={`flex items-center cursor-pointer flex-col justify-center gap-1 px-4 py-3 rounded-lg text-sm transition-colors ${isEditMode && !isLoading
                  ? 'bg-gradient-to-r from-orange-500 to-pink-500 text-white hover:opacity-90'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  }`}
              >
                <FiCheck size={18} />
                {isLoading ? 'Saving...' : 'Done'}
              </button>
            </div>

            <div className="mt-4">
              <button
                onClick={handleCancel}
                className="w-full border cursor-pointer border-orange-500 text-orange-500 px-4 py-2 rounded-full text-sm hover:bg-orange-50 transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      </div>
    </CustomModal>
  );
};

export default CashierBillDemonstrationModal;
