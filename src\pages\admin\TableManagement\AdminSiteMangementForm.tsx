import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  usePostSiteMutation,
  usePutSiteMutation,
  useGetSiteQuery,
} from '../../../store/api/siteManagementApi';
import Swal from 'sweetalert2';

const AdminSiteForm = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEditMode = Boolean(id);
  
  const userId = localStorage.getItem('userId') || '';

  const [postSite, { isLoading: isCreating }] = usePostSiteMutation();
  const [putSite, { isLoading: isUpdating }] = usePutSiteMutation();
  
  const { data: siteData, isLoading: isLoadingSite } = useGetSiteQuery(id || '', {
    skip: !isEditMode,
  });

  const [formData, setFormData] = useState({
    siteName: '',
    noOfTables: '',
    shortDescription: '',
    isActive: false,
    imageUrl: '',
  });
  
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  // Load existing data when in edit mode
  useEffect(() => {
    if (isEditMode && siteData) {
      setFormData({
        siteName: siteData.siteName || '',
        noOfTables: siteData.numberOfTables?.toString() || '',
        shortDescription: siteData.briefDescription || '',
        isActive: siteData.isActive || false,
        imageUrl: siteData.siteImage || '',
      });
    }
  }, [siteData, isEditMode]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleToggleChange = () => {
    setFormData({ ...formData, isActive: !formData.isActive });
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setSelectedFile(e.target.files[0]);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      // Validate form data
      if (!formData.siteName.trim()) {
        Swal.fire({
          title: 'Form Error',
          text: 'Please enter site name',
          icon: 'error',
          confirmButtonColor: '#ea580c',
        });
        return;
      }
      
      if (!formData.noOfTables.trim()) {
        Swal.fire({
          title: 'Form Error',
          text: 'Please enter number of tables',
          icon: 'error',
          confirmButtonColor: '#ea580c',
        });
        return;
      }

      // Check if number of tables is valid
      const tableCount = parseInt(formData.noOfTables);
      if (isNaN(tableCount) || tableCount <= 0) {
        Swal.fire({
          title: 'Form Error',
          text: 'Please enter a valid number of tables',
          icon: 'error',
          confirmButtonColor: '#ea580c',
        });
        return;
      }

      // Prepare form data for API
      const submitFormData = new FormData();
      submitFormData.append('siteName', formData.siteName);
      submitFormData.append('numberOfTables', formData.noOfTables);
      submitFormData.append('briefDescription', formData.shortDescription);
      submitFormData.append('isActive', formData.isActive.toString());
      submitFormData.append('userId', userId);
      
      // Handle image - either file upload or URL
      if (selectedFile) {
        submitFormData.append('siteImage', selectedFile);
      } else if (formData.imageUrl) {
        submitFormData.append('siteImage', formData.imageUrl);
      } else {
        Swal.fire({
          title: 'Form Error',
          text: 'Please upload an image or provide an image URL',
          icon: 'error',
          confirmButtonColor: '#ea580c',
        });
        return;
      }

      // Show loading state
      Swal.fire({
        title: isEditMode ? 'Updating Site...' : 'Creating Site...',
        html: 'Please wait...',
        allowOutsideClick: false,
        allowEscapeKey: false,
        didOpen: () => {
          Swal.showLoading();
        }
      });

      if (isEditMode && id) {
        await putSite({ id, data: submitFormData }).unwrap();
        Swal.fire({
          title: 'Success!',
          text: 'Site updated successfully!',
          icon: 'success',
          confirmButtonColor: '#ea580c',
        }).then(() => {
          navigate('/admin/tables-management/site-management');
        });
      } else {
        await postSite(submitFormData).unwrap();
        Swal.fire({
          title: 'Success!',
          text: 'Site added successfully!',
          icon: 'success',
          confirmButtonColor: '#ea580c',
        }).then(() => {
          navigate('/admin/tables-management/site-management');
        });
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      Swal.fire({
        title: 'Error!',
        text: `Failed to ${isEditMode ? 'update' : 'add'} site. Please try again.`,
        icon: 'error',
        confirmButtonColor: '#ea580c',
      });
    }
  };

  const handleCancel = () => {
    Swal.fire({
      title: 'Are you sure?',
      text: 'You will lose any unsaved changes',
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#ea580c',
      cancelButtonColor: '#d1d5db',
      confirmButtonText: 'Yes, leave page',
      cancelButtonText: 'No, stay here'
    }).then((result) => {
      if (result.isConfirmed) {
        navigate('/admin/tables-management/site-management');
      }
    });
  };

  if (isEditMode && isLoadingSite) {
    return (
      <div className="w-full p-6 flex justify-center items-center">
        <p className="text-gray-600">Loading site data...</p>
      </div>
    );
  }

  return (
    <div className="rounded-lg shadow-sm p-2 bg-gray-50">
      {/* Header */}
      <div className="flex items-center p-2 rounded-xl bg-white border border-gray-200 mb-2">
        <button 
          className="flex items-center text-gray-800 text-xl font-medium cursor-pointer"
          onClick={handleCancel}
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7"></path>
          </svg>
          {isEditMode ? 'Edit Site' : 'Create Site'}
        </button>
      </div>

      <form onSubmit={handleSubmit} className='border border-gray-200 rounded-2xl bg-white'>
        {/* Site Details Section */}
        <div className="bg-orange-50 px-4 py-3 rounded-t-2xl">
          <h2 className="text-base font-medium text-gray-800">Site Details</h2>
        </div>
        
        <div className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            {/* Site Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Site Name
                <span className="text-red-500 ml-1">*</span>
              </label>
              <input
                type="text"
                name="siteName"
                placeholder="Enter Site Name"
                value={formData.siteName}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
                required
              />
            </div>
            
            {/* No Of Tables */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                No Of Tables
                <span className="text-red-500 ml-1">*</span>
              </label>
              <input
                type="text"
                name="noOfTables"
                placeholder="Enter Number of Tables"
                value={formData.noOfTables}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
                required
              />
            </div>
          </div>
          
          {/* Short Description */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Short Description
            </label>
            <textarea
              name="shortDescription"
              placeholder="Enter Description"
              value={formData.shortDescription}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
              rows={3}
            />
          </div>
        </div>
        
        {/* Permissions Section */}
        <div className="bg-orange-50 px-4 py-3">
          <h2 className="text-base font-medium text-gray-800">Permissions</h2>
        </div>
        
        <div className="p-4">
          <div className="flex items-center">
            <div className="relative inline-block w-10 mr-2 align-middle">
              <input
                type="checkbox"
                id="activeToggle"
                className="opacity-0 w-0 h-0"
                checked={formData.isActive}
                onChange={handleToggleChange}
              />
              <label
                htmlFor="activeToggle"
                className={`block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer ${
                  formData.isActive ? 'bg-orange-500' : ''
                }`}
              >
                <span
                  className={`block h-6 w-6 rounded-full bg-white transform transition-transform duration-200 ease-in ${
                    formData.isActive ? 'translate-x-4' : 'translate-x-0'
                  }`}
                ></span>
              </label>
            </div>
            <label htmlFor="activeToggle" className="text-sm font-medium text-gray-700 mt-6">
              Active
            </label>
          </div>
        </div>
        
        {/* Site Image Section */}
        <div className="bg-orange-50 px-4 py-3">
          <h2 className="text-base font-medium text-gray-800">Site Image</h2>
        </div>
        
        <div className="p-4">
          <div className="flex items-start mb-4 ">
            <div className="flex-shrink-0">
              <div className="w-16 h-16 border border-gray-200 bg-gray-100 rounded-full flex items-center justify-center text-orange-500">
                {isEditMode && formData.imageUrl ? (
                  <img 
                    src={formData.imageUrl} 
                    alt="Site" 
                    className="w-16 h-16 object-cover rounded" 
                  />
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                )}
              </div>
            </div>
            
            <div className="ml-4">
              <div>
                <label className="cursor-pointer">
                  <span className="text-sm font-medium text-blue-500">Upload</span>
                  <input 
                    type="file"
                    className="hidden"
                    accept=".svg,.png,.jpg,.gif"
                    onChange={handleFileUpload}
                  />
                </label>
                <span className="ml-2 text-sm text-gray-500">Site Image</span>
              </div>
              <p className="text-xs text-gray-500">
                SVG, PNG, JPG or GIF (max. 800 x 800px)
              </p>
              {selectedFile && (
                <p className="mt-1 text-xs text-gray-700">Selected: {selectedFile.name}</p>
              )}
            </div>
          </div>
          
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Or Enter Image URL
            </label>
            <input
              type="url"
              name="imageUrl"
              placeholder="Add Image URL"
              value={formData.imageUrl}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
            />
          </div>
        </div>
        
        {/* Action Buttons */}
        <div className="flex justify-end space-x-4 p-4 border-t border-gray-100">
          <button
            type="button"
            onClick={handleCancel}
            className="px-6 py-2 border border-orange-500 text-orange-500 rounded-lg cursor-pointer hover:bg-orange-50 focus:outline-none"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isCreating || isUpdating}
            className={`px-6 py-2 ${
              (isCreating || isUpdating) ? 'bg-orange-300' : 'bg-orange-500 hover:bg-orange-600'
            } text-white rounded-lg cursor-pointer focus:outline-none`}
          >
            {isEditMode ? 
              (isUpdating ? 'Updating...' : 'Update Site') : 
              (isCreating ? 'Adding...' : 'Add Site')
            }
          </button>
        </div>
      </form>
    </div>
  );
};

export default AdminSiteForm;