import { useState, useEffect } from 'react';
import { Search, PlusCircle, Edit, Trash2, ChevronLeft, ChevronRight } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import {
  useGetIngredientsCategoriesQuery,
  useDeleteIngredientsCategoryMutation,
} from "../../../store/api/ingredientsCategoryApi";
// Removed toast import as we're using SweetAlert2 for all notifications
import Swal from 'sweetalert2';

const IngredientsCategory = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(5);

  const userId = localStorage.getItem('userId') || '';

  const { data: categoriesData, isLoading, error, refetch } = useGetIngredientsCategoriesQuery(userId);
  const [deleteCategory, { isLoading: isDeleting }] = useDeleteIngredientsCategoryMutation();

  // Filter categories based on search term
  const filteredCategories = categoriesData?.filter(category =>
    category.name.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentCategories = filteredCategories.slice(indexOfFirstItem, indexOfLastItem);

  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);

  const handleDelete = (id: string, categoryName: string) => {
    Swal.fire({
      title: 'Are you sure?',
      text: `Do you want to delete "${categoryName}" category?`,
      icon: 'warning',
      showCancelButton: true,
       confirmButtonColor: '#3085d6',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'No, cancel!'
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          await deleteCategory(id).unwrap();
          
          Swal.fire({
            title: 'Deleted!',
            text: 'Category has been deleted successfully.',
            icon: 'success',
            confirmButtonColor: '#4f46e5'
          });
          
          refetch();
        } catch (error) {
          console.error('Failed to delete category:', error);
          
          Swal.fire({
            title: 'Error!',
            text: 'Failed to delete category.',
            icon: 'error',
            confirmButtonColor: '#4f46e5'
          });
        }
      }
    });
  };

  const handleEdit = (id: string) => {
    navigate(`/admin/stock-management/ingredients-category/ingredients-category-form/${id}`);
  };

  useEffect(() => {
    if (error) {
      Swal.fire({
        title: 'Error!',
        text: 'Failed to fetch ingredients categories',
        icon: 'error',
        confirmButtonColor: '#4f46e5'
      });
      console.error('Error fetching categories:', error);
    }
  }, [error]);

  return (
    <div className="p-4 bg-gray-50">
      <div className="flex items-center md:flex-row flex-col justify-between mb-4 border border-gray-200 p-2 bg-white rounded-2xl">
        <h1 className="lg:text-3xl text-2xl p-3 font-medium">Ingredients Category</h1>

        <div className="flex items-center sm:flex-row flex-col gap-2">
          <div className="relative">
            <input
              type="text"
              placeholder="Search Ingredients Category"
              className="pl-10 pr-4 py-2 border border-gray-300 bg-gray-50 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          </div>

          <button
            onClick={() => navigate('/admin/stock-management/ingredients-category/ingredients-category-form')}
            className="flex items-center gap-1 bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md"
          >
            <PlusCircle className="w-4 h-4" />
            Add Ingredients Category
          </button>
        </div>
      </div>

      {isLoading ? (
        <div className="bg-gray-50 p-4 min-h-screen flex justify-center items-center">
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-orange-500"></div>
            <span>Loading ingredient categories...</span>
          </div>
        </div>
      ) : error ? (
        <p className="text-center text-red-500">Failed to load categories.</p>
      ) : (
        <>
          <div className="bg-white rounded-md shadow overflow-x-auto">
            <table className="min-w-full bg-orange-50">
              <thead className="bg-orange-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-bold text-gray-900 uppercase tracking-wider w-20">No</th>
                  <th className="px-6 py-3 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">Category Name</th>
                  <th className="px-6 py-3 text-right text-xs font-bold text-gray-900 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {currentCategories.map((category, index) => (
                  <tr key={category.id} className="hover:bg-orange-50 text-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                      {indexOfFirstItem + index + 1}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      {category.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        className="text-blue-400 hover:text-blue-600 mr-3"
                        onClick={() => handleEdit(category.id)}
                        disabled={isDeleting}
                      >
                        <Edit className="w-5 h-5" />
                      </button>
                      <button
                        className={`text-red-400 hover:text-red-600 ${isDeleting ? "cursor-not-allowed opacity-50" : ""}`}
                        onClick={() => handleDelete(category.id, category.name)}
                        disabled={isDeleting}
                      >
                        <Trash2 className="w-5 h-5" />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          <div className="flex items-center justify-start mt-4 gap-2">
            <button
              onClick={() => paginate(currentPage - 1)}
              disabled={currentPage === 1}
              className={`flex items-center justify-center w-8 h-8 rounded-md border ${currentPage === 1
                  ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                  : "bg-white text-gray-700 hover:bg-gray-50"
                }`}
            >
              <ChevronLeft size={16} />
            </button>

            {Array.from(
              { length: Math.ceil(filteredCategories.length / itemsPerPage) },
              (_, i) => i + 1
            ).map((number) => (
              <button
                key={number}
                onClick={() => paginate(number)}
                className={`w-8 h-8 rounded-md flex items-center justify-center border ${currentPage === number
                    ? "bg-orange-500 text-white"
                    : "bg-white text-gray-700 hover:bg-gray-50"
                  }`}
              >
                {number}
              </button>
            ))}

            <button
              onClick={() => paginate(currentPage + 1)}
              disabled={indexOfLastItem >= filteredCategories.length}
              className={`flex items-center justify-center w-8 h-8 rounded-md border ${indexOfLastItem >= filteredCategories.length
                  ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                  : "bg-white text-gray-700 hover:bg-gray-50"
                }`}
            >
              <ChevronRight size={16} />
            </button>
          </div>
        </>
      )}
    </div>
  );
};

export default IngredientsCategory;