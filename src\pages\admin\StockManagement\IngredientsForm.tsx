import React, { useState, useEffect } from 'react';
import { ChevronLeft, Calendar, X, Search } from 'lucide-react';
import { useNavigate, useParams } from 'react-router-dom';
import Swal from 'sweetalert2'; // Import SweetAlert2
import {
  usePostIngredientMutation,
  usePutIngredientMutation,
  useGetIngredientQuery,
  useGetIngredientsQuery,
} from '../../../store/api/ingredientsApi';
import { useGetUnitOfMeasurementsQuery } from '../../../store/api/unitOfMeasurementApi';
import { useGetSuppliersQuery } from '../../../store/api/supplierApi';
import { useGetIngredientsCategoriesQuery } from '../../../store/api/ingredientsCategoryApi';

interface SupplierOption {
  id: string;
  name: string;
}

interface UnitOption {
  id: string;
  name: string;
}

interface CategoryOption {
  id: string;
  name: string;
}

const CreateNewIngredient: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEditMode = Boolean(id);
  const userId = localStorage.getItem('userId') || '';

  // RTK Query hooks
  const { data: ingredient } = useGetIngredientQuery(id || '', { skip: !isEditMode });
  const { data: _ } = useGetIngredientsQuery(userId);
  const [postIngredient, { isLoading: isCreating }] = usePostIngredientMutation();
  const [putIngredient, { isLoading: isUpdating }] = usePutIngredientMutation();

  
  // Fetch dynamic data
  const { data: units = [] } = useGetUnitOfMeasurementsQuery(userId);
  const { data: suppliers = [] } = useGetSuppliersQuery(userId);
  const { data: categories = [] } = useGetIngredientsCategoriesQuery(userId);

  // Form state
  const [ingredientId, setIngredientId] = useState<string>('');
  const [ingredientName, setIngredientName] = useState<string>('');
  const [unitPrice, setUnitPrice] = useState<string>('');
  const [shelfLife, setShelfLife] = useState<string>('');
  const [currentStock, setCurrentStock] = useState<string>('');
  const [storageInstruction, setStorageInstruction] = useState<string>('');
  const [thresholdLevel, setThresholdLevel] = useState<string>('');
  const [alternative, setAlternative] = useState<string>('');
  const [notes, setNotes] = useState<string>('');
  const [expiryDate, setExpiryDate] = useState<string>('');
  const [nutritionalInfo, setNutritionalInfo] = useState<string>('');
  const [visibility, setVisibility] = useState<boolean>(true);

  // Search state
  const [unitSearch, setUnitSearch] = useState<string>('');
  const [supplierSearch, setSupplierSearch] = useState<string>('');
  const [categorySearch, setCategorySearch] = useState<string>('');

  // Dropdown states - Now storing ID and display name separately
  const [selectedUnitId, setSelectedUnitId] = useState<string>('');
  const [selectedUnitName, setSelectedUnitName] = useState<string>('Select Unit');
  
  const [selectedSupplierId, setSelectedSupplierId] = useState<string>('');
  const [selectedSupplierName, setSelectedSupplierName] = useState<string>('Select Supplier');
  
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>('');
  const [selectedCategoryName, setSelectedCategoryName] = useState<string>('Select Category');
  
  const [isUnitDropdownOpen, setIsUnitDropdownOpen] = useState<boolean>(false);
  const [isSupplierDropdownOpen, setIsSupplierDropdownOpen] = useState<boolean>(false);
  const [isCategoryDropdownOpen, setIsCategoryDropdownOpen] = useState<boolean>(false);

  // Error states
  const [thresholdError, setThresholdError] = useState<string>('');


  // Populate form data if in edit mode
  useEffect(() => {
    if (isEditMode && ingredient) {
      setIngredientId(ingredient.IngredientID || '');
      setIngredientName(ingredient.IngredientName || '');
      setUnitPrice(ingredient.UnitPrice?.toString() || '');
      setShelfLife(ingredient.ShelfLife || '');
      setCurrentStock(ingredient.CurrentStock?.toString() || '');
      setStorageInstruction(ingredient.StorageInstructions || '');
      setThresholdLevel(ingredient.ThresholdLevel?.toString() || '');
      setAlternative(ingredient.Alternative || '');
      setNotes(ingredient.Notes || '');
      setExpiryDate(ingredient.Expiry || '');
      setNutritionalInfo(ingredient.NutritionalInformation || '');
      setVisibility(ingredient.Active !== false);
      
      // Handle supplier - Check if it's an object (from the API response format)
      if (ingredient.supplierId && typeof ingredient.supplierId === 'object' && ingredient.supplierId._id) {
        setSelectedSupplierId(ingredient.supplierId._id);
        setSelectedSupplierName(ingredient.supplierId.SupplierName || 'Select Supplier');
      } else if (ingredient.supplierId) {
        // If it's just an ID string
        setSelectedSupplierId(ingredient.supplierId);
        // Find supplier name for display
        const supplier = suppliers.find(s => s.id === ingredient.supplierId);
        if (supplier) {
          setSelectedSupplierName(supplier.SupplierName);
        }
      }
      
      // Handle unit of measurement - Check if it's an object (from the API response format)
      if (ingredient.UnitofMeasurement && typeof ingredient.UnitofMeasurement === 'object' && ingredient.UnitofMeasurement._id) {
        setSelectedUnitId(ingredient.UnitofMeasurement._id);
        setSelectedUnitName(ingredient.UnitofMeasurement.name || 'Select Unit');
      } else if (ingredient.UnitofMeasurement) {
        // If it's just an ID string or name
        const unit = units.find(u => u.id === ingredient.UnitofMeasurement || u.name === ingredient.UnitofMeasurement);
        if (unit) {
          setSelectedUnitId(unit.id);
          setSelectedUnitName(unit.name);
        }
      }
      
      // Handle category type - Check if it's an object (from the API response format)
      if (ingredient.CategoryType && typeof ingredient.CategoryType === 'object' && ingredient.CategoryType._id) {
        setSelectedCategoryId(ingredient.CategoryType._id);
        setSelectedCategoryName(ingredient.CategoryType.name || 'Select Category');
      } else if (ingredient.CategoryType) {
        // If it's just an ID string or name
        const category = categories.find(c => c.id === ingredient.CategoryType || c.name === ingredient.CategoryType);
        if (category) {
          setSelectedCategoryId(category.id);
          setSelectedCategoryName(category.name);
        }
      }
    }
  }, [isEditMode, ingredient, units, suppliers, categories]);

  // Filter units based on search
  const filteredUnits = units
    .filter(unit => unit.name.toLowerCase().includes(unitSearch.toLowerCase()))
    .map(unit => ({
      id: unit.id,
      name: unit.name
    }));

  // Filter suppliers based on search
  const filteredSuppliers = suppliers
    .filter(supplier => supplier.SupplierName.toLowerCase().includes(supplierSearch.toLowerCase()))
    .map(supplier => ({
      id: supplier.id,
      name: supplier.SupplierName
    }));

  // Filter categories based on search
  const filteredCategories = categories
    .filter(category => category.name.toLowerCase().includes(categorySearch.toLowerCase()))
    .map(category => ({
      id: category.id,
      name: category.name
    }));

  const handleUnitChange = (unit: UnitOption) => {
    setSelectedUnitId(unit.id);
    setSelectedUnitName(unit.name);
    setIsUnitDropdownOpen(false);
  };

  const handleSupplierChange = (supplier: SupplierOption) => {
    setSelectedSupplierId(supplier.id);
    setSelectedSupplierName(supplier.name);
    setIsSupplierDropdownOpen(false);
  };

  const handleCategoryChange = (category: CategoryOption) => {
    setSelectedCategoryId(category.id);
    setSelectedCategoryName(category.name);
    setIsCategoryDropdownOpen(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    if (!thresholdLevel) {
      setThresholdError('Threshold Level is required.');
      return;
    }

    try {
      const formData = {
        // Don't send IngredientID when creating new - backend will generate it
        ...(isEditMode && { IngredientID: ingredientId }),
        IngredientName: ingredientName,
        UnitPrice: parseFloat(unitPrice),
        CurrentStock: parseFloat(currentStock),
        // Send IDs instead of names
        UnitofMeasurement: selectedUnitId || null,  // Send ID instead of name
        supplierId: selectedSupplierId || null,
        CategoryType: selectedCategoryId || null,  // Send ID instead of name
        Expiry: expiryDate,
        ThresholdLevel: parseFloat(thresholdLevel),
        ShelfLife: shelfLife,
        StorageInstructions: storageInstruction,
        Alternative: alternative,
        NutritionalInformation: nutritionalInfo,
        Notes: notes,
        Active: visibility,
        userId: userId
      };

      if (isEditMode && id) {
        // Update existing ingredient
        await putIngredient({
          id,
          formData: {
            ...formData,
            _id: id,
          }
        }).unwrap();
        
        // Show success message with SweetAlert2
        Swal.fire({
          icon: 'success',
          title: 'Success!',
          text: 'Ingredient updated successfully!',
          confirmButtonColor: '#f97316', // Orange color to match your theme
        });
      } else {
        // Create new ingredient
        await postIngredient(formData).unwrap();
        
        // Show success message with SweetAlert2
        Swal.fire({
          icon: 'success',
          title: 'Success!',
          text: 'Ingredient created successfully!',
          confirmButtonColor: '#f97316', // Orange color to match your theme
        });
      }
      navigate('/admin/stock-management/ingredients');
    } catch (error) {
      console.error(isEditMode ? 'Error updating ingredient:' : 'Error creating ingredient:', error);
      
      // Show error message with SweetAlert2
      Swal.fire({
        icon: 'error',
        title: 'Error!',
        text: isEditMode ? 'Failed to update ingredient.' : 'Failed to create ingredient.',
        confirmButtonColor: '#f97316', // Orange color to match your theme
      });
    }
  };

  const handleCancel = () => {
    navigate('/admin/stock-management/ingredients');
  };

  const isLoading = isCreating || isUpdating;

  return (
    <div className="p-4 bg-gray-50">
      {/* Header */}
      <div className="flex items-center p-2 mb-4 rounded-2xl border border-gray-200 bg-white">
        <ChevronLeft
          onClick={handleCancel}
          className="w-5 h-5 mr-2 cursor-pointer" 
        />
        <h1 className="text-base font-medium">
          {isEditMode ? 'Edit Ingredient' : 'Create New Ingredient Data Entry'}
        </h1>
      </div>

      <form onSubmit={handleSubmit}>
        {/* Ingredient Details Section */}
        <div className="mb-6 bg-white border border-gray-200 rounded-xl">
          <div className="bg-orange-50 rounded-t-xl p-3 mb-4">
            <h2 className="text-sm font-medium">Ingredient Details</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-3 mb-4">
            {/* Ingredient ID - Only show in edit mode */}
            {isEditMode && (
              <div>
                <label className="block text-sm mb-1">
                  Ingredient ID
                </label>
                <input
                  type="text"
                  placeholder="Enter Ingredient ID"
                  className="w-full border border-gray-200 rounded-md p-2 text-sm"
                  value={ingredientId}
                  onChange={(e) => setIngredientId(e.target.value)}
                  disabled={true}
                />
              </div>
            )}

            {/* Ingredient Name */}
            <div>
              <label className="block text-sm mb-1">
                Ingredient Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                placeholder="Enter Ingredient Name"
                className="w-full border border-gray-200 rounded-md p-2 text-sm"
                value={ingredientName}
                onChange={(e) => setIngredientName(e.target.value)}
                required
              />
            </div>

            {/* Unit of Measurement */}
            <div>
              <label className="block text-sm mb-1">
                Unit of Measurement
              </label>
              <div className="relative">
                <div 
                  className="w-full border border-gray-200 rounded-md p-2 text-sm flex justify-between items-center cursor-pointer"
                  onClick={() => setIsUnitDropdownOpen(!isUnitDropdownOpen)}
                >
                  <span>{selectedUnitName}</span>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
                
                {isUnitDropdownOpen && (
                  <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg">
                    <div className="p-2 bg-gray-100 flex items-center">
                      <Search className="w-4 h-4 text-gray-400 mr-2" />
                      <input 
                        type="text" 
                        placeholder="Search unit" 
                        className="w-full text-sm outline-none"
                        value={unitSearch}
                        onChange={(e) => setUnitSearch(e.target.value)}
                      />
                      <X 
                        className="w-4 h-4 text-gray-400 cursor-pointer" 
                        onClick={() => setIsUnitDropdownOpen(false)}
                      />
                    </div>
                    <ul className="max-h-40 overflow-y-auto">
                      {filteredUnits.length > 0 ? (
                        filteredUnits.map((unit) => (
                          <li 
                            key={unit.id} 
                            className="px-4 py-2 hover:bg-gray-100 cursor-pointer text-sm"
                            onClick={() => handleUnitChange(unit)}
                          >
                            {unit.name}
                          </li>
                        ))
                      ) : (
                        <li className="px-4 py-2 text-gray-500 text-sm">No units found</li>
                      )}
                    </ul>
                  </div>
                )}
              </div>
            </div>

            {/* Unit Price */}
            <div>
              <label className="block text-sm mb-1">
                Unit Price <span className="text-red-500">*</span>
              </label>
              <input
                type="number"
                placeholder="Enter Unit Price"
                className="w-full border border-gray-200 rounded-md p-2 text-sm"
                value={unitPrice}
                onChange={(e) => setUnitPrice(e.target.value)}
                required
              />
            </div>

            {/* Current Stock */}
            <div>
              <label className="block text-sm mb-1">
                Current Stock <span className="text-red-500">*</span>
              </label>
              <input
                type="number"
                placeholder="Enter Current Stock"
                className="w-full border border-gray-200 rounded-md p-2 text-sm"
                value={currentStock}
                onChange={(e) => setCurrentStock(e.target.value)}
                required
              />
            </div>

            {/* Threshold Level */}
            <div>
              <label className="block text-sm mb-1">
                Threshold Level <span className="text-red-500">*</span>
              </label>
              <input
                type="number"
                placeholder="Enter Threshold Level"
                className={`w-full border ${thresholdError ? 'border-red-500' : 'border-gray-200'} rounded-md p-2 text-sm`}
                value={thresholdLevel}
                onChange={(e) => {
                  setThresholdLevel(e.target.value);
                  setThresholdError('');
                }}
                required
              />
              {thresholdError && (
                <p className="text-red-500 text-xs mt-1">{thresholdError}</p>
              )}
            </div>

            {/* Shelf Life */}
            <div>
              <label className="block text-sm mb-1">
                Shelf Life <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                placeholder="Enter Shelf Life"
                className="w-full border border-gray-200 rounded-md p-2 text-sm"
                value={shelfLife}
                onChange={(e) => setShelfLife(e.target.value)}
                required
              />
            </div>

            {/* Storage Instruction */}
            <div>
              <label className="block text-sm mb-1">
                Storage Instruction <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                placeholder="Enter Storage Instruction"
                className="w-full border border-gray-200 rounded-md p-2 text-sm"
                value={storageInstruction}
                onChange={(e) => setStorageInstruction(e.target.value)}
                required
              />
            </div>

            {/* Category Type */}
            <div>
              <label className="block text-sm mb-1">
                Category Type
              </label>
              <div className="relative">
                <div 
                  className="w-full border border-gray-200 rounded-md p-2 text-sm flex justify-between items-center cursor-pointer"
                  onClick={() => setIsCategoryDropdownOpen(!isCategoryDropdownOpen)}
                >
                  <span>{selectedCategoryName}</span>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
                
                {isCategoryDropdownOpen && (
                  <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg">
                    <div className="p-2 bg-gray-100 flex items-center">
                      <Search className="w-4 h-4 text-gray-400 mr-2" />
                      <input 
                        type="text" 
                        placeholder="Search category" 
                        className="w-full text-sm outline-none"
                        value={categorySearch}
                        onChange={(e) => setCategorySearch(e.target.value)}
                      />
                      <X 
                        className="w-4 h-4 text-gray-400 cursor-pointer" 
                        onClick={() => setIsCategoryDropdownOpen(false)}
                      />
                    </div>
                    <ul className="max-h-40 overflow-y-auto">
                      {filteredCategories.length > 0 ? (
                        filteredCategories.map((category) => (
                          <li 
                            key={category.id} 
                            className="px-4 py-2 hover:bg-gray-100 cursor-pointer text-sm"
                            onClick={() => handleCategoryChange(category)}
                          >
                            {category.name}
                          </li>
                        ))
                      ) : (
                        <li className="px-4 py-2 text-gray-500 text-sm">No categories found</li>
                      )}
                    </ul>
                  </div>
                )}
              </div>
            </div>

            {/* Supplier */}
            <div>
              <label className="block text-sm mb-1">
                Supplier
              </label>
              <div className="relative">
                <div 
                  className="w-full border border-gray-200 rounded-md p-2 text-sm flex justify-between items-center cursor-pointer"
                  onClick={() => setIsSupplierDropdownOpen(!isSupplierDropdownOpen)}
                >
                  <span>{selectedSupplierName}</span>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
                
                {isSupplierDropdownOpen && (
                  <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg">
                    <div className="p-2 bg-gray-100 flex items-center">
                      <Search className="w-4 h-4 text-gray-400 mr-2" />
                      <input 
                        type="text" 
                        placeholder="Search supplier" 
                        className="w-full text-sm outline-none"
                        value={supplierSearch}
                        onChange={(e) => setSupplierSearch(e.target.value)}
                      />
                      <X 
                        className="w-4 h-4 text-gray-400 cursor-pointer" 
                        onClick={() => setIsSupplierDropdownOpen(false)}
                      />
                    </div>
                    <ul className="max-h-40 overflow-y-auto">
                      {filteredSuppliers.length > 0 ? (
                        filteredSuppliers.map((supplier) => (
                          <li 
                            key={supplier.id} 
                            className="px-4 py-2 hover:bg-gray-100 cursor-pointer text-sm"
                            onClick={() => handleSupplierChange(supplier)}
                          >
                            {supplier.name}
                          </li>
                        ))
                      ) : (
                        <li className="px-4 py-2 text-gray-500 text-sm">No suppliers found</li>
                      )}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Ingredient Additional Details Section */}
        <div className="mb-6 bg-white border border-gray-200 rounded-xl">
          <div className="bg-orange-50 rounded-t-xl p-3 mb-4">
            <h2 className="text-sm font-medium">Ingredient Additional Details</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-3">
            {/* Alternative */}
            <div>
              <label className="block text-sm mb-1">
                Alternative <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                placeholder="Enter Alternative"
                className="w-full border border-gray-200 rounded-md p-2 text-sm"
                value={alternative}
                onChange={(e) => setAlternative(e.target.value)}
                required
              />
            </div>

            {/* Nutritional Information */}
            <div>
              <label className="block text-sm mb-1">
                Nutritional Information <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                placeholder="Enter Nutritional Information"
                className="w-full border border-gray-200 rounded-md p-2 text-sm"
                value={nutritionalInfo}
                onChange={(e) => setNutritionalInfo(e.target.value)}
                required
              />
            </div>

            {/* Expiry */}
            <div>
              <label className="block text-sm mb-1">
                Expiry <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <input
                  type="date"
                  placeholder="mm/dd/yyyy"
                  className="w-full border border-gray-200 rounded-md p-2 text-sm pr-8"
                  value={expiryDate}
                  onChange={(e) => setExpiryDate(e.target.value)}
                  required
                />
                <Calendar className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              </div>
            </div>

            {/* Notes */}
            <div className="md:col-span-2">
              <label className="block text-sm mb-1">
                Notes <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                placeholder="Enter Notes"
                className="w-full border border-gray-200 rounded-md p-2 text-sm"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                required
              />
            </div>
          </div>
        </div>

        {/* Permissions Section */}
        <div className="mb-6 bg-white border border-gray-200 rounded-xl">
          <div className="bg-orange-50 rounded-t-xl p-3 mb-4">
            <h2 className="text-sm font-medium">Permissions</h2>
          </div>

          <div className="flex items-center p-3">
            <div className="mr-2">
              <label className="relative inline-flex items-center cursor-pointer">
                <input 
                  type="checkbox" 
                  className="sr-only peer" 
                  checked={visibility}
                  onChange={() => setVisibility(!visibility)}
                />
                <div className={`w-11 h-6 rounded-full peer ${visibility ? 'bg-orange-500' : 'bg-gray-200'} peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all`}></div>
              </label>
            </div>
            <span className="text-sm">Visibility</span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-2">
          <button 
            type="button"
            onClick={handleCancel}
            className="px-6 py-2 border border-gray-200 rounded-md text-sm">
            Cancel
          </button>
          <button 
            type="submit"
            disabled={isLoading}
            className={`px-6 py-2 rounded-md text-white text-sm ${
              isLoading ? 'bg-orange-300 cursor-not-allowed' : 'bg-orange-500 hover:bg-orange-600'
            }`}
          >
            {isLoading
              ? isEditMode ? 'Updating...' : 'Adding...'
              : isEditMode ? 'Update Ingredient' : 'Add Ingredient'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default CreateNewIngredient;