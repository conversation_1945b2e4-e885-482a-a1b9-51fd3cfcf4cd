// Updated Interface - Change hasLampixDevice to string | boolean to handle both cases
export interface TableManagement {
  _id?: string;
  tableNo: string;
  tableimg?: string;
  tableName: string;
  location: string | object;
  description?: string;
  hasLampixDevice?: string | boolean; // Updated to handle both string and boolean
  userId: string;
  floor: string;
  Status?: string;
  height?: number;
  width?: number;
  x?: number;
  y?: number;
  capacity?: number;
}

// Updated component with proper type checking
import { useState, useEffect, type SetStateAction } from "react";
import { useNavigate } from "react-router-dom";
import { Tooltip } from "react-tooltip";
import "react-tooltip/dist/react-tooltip.css";
import {
  useGetTablesQuery,
  useDeleteTableMutation,
} from "../../../store/api/TableManagementApi";
import { Trash2, Edit } from "lucide-react";
import Swal from "sweetalert2";

const AdminTables = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);

  const userId = localStorage.getItem("userId") || "";

  const {
    data: tablesData = [],
    isLoading,
    error,
    refetch,
  } = useGetTablesQuery(userId);

  const [deleteTable] = useDeleteTableMutation();

  const tables = Array.isArray(tablesData) ? tablesData : [];

  const handleSearchChange = (e: {
    target: { value: SetStateAction<string> };
  }) => {
    setSearchTerm(e.target.value);
  };

  const handleAddTable = () => {
    navigate("/admin/tables-management/tables/table-form");
  };
// Replace your handleDownloadMetaData function with this:

const handleDownloadMetaData = () => {
  try {
    if (!tables || tables.length === 0) {
      Swal.fire({
        title: "No Data",
        text: "No table data available to download",
        icon: "warning",
        confirmButtonText: "OK",
      });
      return;
    }

    // Prepare the data for download
    const metaData = tables.map((table, index) => ({
      No: index + 1,
      TableNo: table.tableNo || '',
      TableName: table.tableName || '',
      Location: typeof table.location === 'object' 
        ? getSiteName(table.location) 
        : table.location || '',
      Description: table.description || '',
      Capacity: table.capacity || '',
      Floor: table.floor || '',
      Status: isLampixActive(table.hasLampixDevice) ? 'Active' : 'Inactive',
      HasLampixDevice: table.hasLampixDevice || false,
      Height: table.height || '',
      Width: table.width || '',
      X_Position: table.x || '',
      Y_Position: table.y || '',
      CreatedDate: new Date().toLocaleDateString(),
    }));

    // Convert to CSV format
    const csvContent = convertToCSV(metaData);
    
    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    
    link.setAttribute('href', url);
    link.setAttribute('download', `table_metadata_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    Swal.fire({
      title: "Success!",
      text: "Table meta data downloaded successfully",
      icon: "success",
      confirmButtonText: "OK",
    });

  } catch (error) {
    console.error('Error downloading metadata:', error);
    Swal.fire({
      title: "Error!",
      text: "Failed to download table meta data",
      icon: "error",
      confirmButtonText: "OK",
    });
  }
};

// Helper function to convert data to CSV format
const convertToCSV = (data: any) => {
  if (!data || data.length === 0) return '';
  
  // Get headers from the first object
  const headers = Object.keys(data[0]);
  
  // Create CSV header row
  const csvHeader = headers.join(',');
  
  // Create CSV data rows
  const csvRows = data.map((row: any) => {
    return headers.map(header => {
      const value = row[header];
      // Handle values that might contain commas, quotes, or newlines
      if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
        return `"${value.replace(/"/g, '""')}"`;
      }
      return value;
    }).join(',');
  });
  
  // Combine header and rows
  return [csvHeader, ...csvRows].join('\n');
};

  const handleEdit = (id: string) => {
    navigate(`/admin/tables-management/tables/table-form/${id}`);
  };

  const confirmDelete = async (id: string) => {
    try {
      await deleteTable(id).unwrap();
      Swal.fire({
        title: "Success!",
        text: "Table deleted successfully",
        icon: "success",
        confirmButtonText: "OK",
      });
      refetch();
    } catch (error) {
      console.error("Failed to delete table:", error);
      Swal.fire({
        title: "Error!",
        text: "Failed to delete table",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  const handleDelete = (id: string) => {
    Swal.fire({
      title: "Are you sure?",
      text: "Do you want to delete this table?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#d33",
      cancelButtonColor: "#3085d6",
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "No, cancel!",
    }).then((result) => {
      if (result.isConfirmed) {
        confirmDelete(id);
      }
    });
  };

  useEffect(() => {
    if (error) {
      Swal.fire({
        title: "Error!",
        text: "Failed to fetch tables",
        icon: "error",
        confirmButtonText: "OK",
      });
      console.error("Error fetching tables:", error);
    }
  }, [error]);

  // Filter tables based on search term
  const filteredTables = tables.filter((table) => {
    if (!table) return false;

    const search = searchTerm.toLowerCase();
    const tableName =
      typeof table.tableName === "string" ? table.tableName.toLowerCase() : "";
    const tableNo =
      typeof table.tableNo === "string" ? table.tableNo.toLowerCase() : "";
    const location =
      typeof table.location === "string" ? table.location.toLowerCase() : "";
    const description =
      typeof table.description === "string"
        ? table.description.toLowerCase()
        : "";

    return (
      tableName.includes(search) ||
      tableNo.includes(search) ||
      location.includes(search) ||
      description.includes(search)
    );
  });

  // Calculate pagination
  const tablesPerPage = 10;
  const indexOfLastTable = currentPage * tablesPerPage;
  const indexOfFirstTable = indexOfLastTable - tablesPerPage;
  const currentTables = filteredTables.slice(
    indexOfFirstTable,
    indexOfLastTable
  );
  const totalPages = Math.ceil(filteredTables.length / tablesPerPage);

  const handlePageChange = (page: number) => {
    if (page > 0 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  // Ensure that table data is safely rendered
  const safeRender = (value: string | number | null | undefined) => {
    if (value === null || value === undefined) {
      return "";
    }
    if (typeof value === "object") {
      return JSON.stringify(value);
    }
    return value;
  };

  // Helper function to safely access the siteName property
  const getSiteName = (location: string | object | undefined) => {
    if (!location || typeof location !== "object") {
      return "";
    }

    return (location as { siteName?: string }).siteName || "";
  };

  // Helper function to check if Lampix device is active
  const isLampixActive = (
    hasLampixDevice: string | boolean | undefined
  ): boolean => {
    if (typeof hasLampixDevice === "boolean") {
      return hasLampixDevice;
    }
    if (typeof hasLampixDevice === "string") {
      return hasLampixDevice.toLowerCase() === "true";
    }
    return false;
  };

  return (
    <>
      <div className="rounded bg-gray-50 p-3">
        <div className="flex lg:flex-row flex-col justify-between items-center rounded-xl p-2 border mb-3 bg-white border-gray-200">
          <h2 className="text-3xl p-3 font-bold text-gray-800">Tables</h2>

          <div className="flex md:flex-row flex-col lg-space-y-0 space-y-2 lg:space-x-2 space-x-1">
            <div className="relative">
              <input
                type="text"
                placeholder="Search Tables"
                value={searchTerm}
                onChange={handleSearchChange}
                className="lg:pl-2 pl-3 pr-10 py-2 border border-gray-300 bg-gray-50 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
              />
              <div className="absolute inset-y-0 right-0 flex items-center pr-10 md:pr-3 pointer-events-none">
                <svg
                  className="h-5 w-5 text-gray-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </div>
            </div>

            <button
              onClick={handleDownloadMetaData}
              className="bg-orange-50 text-orange-500  cursor-pointer border border-orange-500 hover:text-white hover:bg-orange-500 rounded-md px-4 py-2 flex items-center"
            >
              Download Table Meta Data
              <svg
                className="ml-2 h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                />
              </svg>
            </button>
            <div>
              <button
                onClick={handleAddTable}
                className="bg-orange-500 cursor-pointer hover:bg-orange-600 text-white rounded-md px-4 py-2 flex items-center"
              >
                Add Table
                <svg
                  className="ml-2 h-4 w-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* Table */}
        {isLoading ? (
          <div className="flex justify-center items-start bg-white h-screen pt-[20vh]">
            <div className="flex flex-col items-center">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500 mb-4"></div>
              <p className="text-gray-600 font-medium">Loading Tables...</p>
            </div>
          </div>
        ) : error ? (
          <div className="flex justify-center items-center h-40">
            <p className="text-red-500">
              Failed to load tables. Please try again later.
            </p>
          </div>
        ) : filteredTables.length === 0 ? (
          <div className="bg-white rounded-lg shadow p-6 text-center">
            <p className="text-gray-500">No tables found.</p>
          </div>
        ) : (
          <div className="overflow-x-auto rounded-2xl border border-gray-200">
            <table className="min-w-full">
              <thead className="bg-orange-50">
                <tr>
                  <th className="px-6 py-5 text-left text-sm font-semibold text-gray-700  tracking-wider w-20">
                    No
                  </th>
                  <th className="px-6 py-5 text-left text-sm font-semibold text-gray-700  tracking-wider">
                    Table Name
                  </th>
                  <th className="px-6 py-5 text-left text-sm font-semibold text-gray-700  tracking-wider">
                    Seating Capacity
                  </th>
                  <th className="px-6 py-5 text-left text-sm font-semibold text-gray-700  tracking-wider">
                    Site Information
                  </th>
                  <th className="px-6 py-5 text-left text-sm font-semibold text-gray-700  tracking-wider">
                    Description
                  </th>
                  <th className="px-6 py-5 text-left text-sm font-semibold text-gray-700  tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-5 text-left text-sm font-semibold text-gray-700  tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {currentTables.map((table) => (
                  <tr
                    key={table._id || Math.random()}
                    className="hover:bg-orange-50"
                  >
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {safeRender(table.tableNo)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm">
                        {safeRender(table.tableName)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                      {safeRender(table.capacity)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm">
                        {typeof table.location === "object"
                          ? safeRender(getSiteName(table.location))
                          : safeRender(table.location)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                      {safeRender(table.description)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {table.hasLampixDevice !== undefined && (
                        <span
                          className={`px-5 inline-flex text-xs leading-5 font-semibold rounded-lg ${
                            isLampixActive(table.hasLampixDevice)
                              ? "bg-green-50 text-green-600 px-6 border border-green-400"
                              : "bg-orange-50 text-orange-500 border border-orange-400"
                          }`}
                        >
                          {isLampixActive(table.hasLampixDevice)
                            ? "Active"
                            : "Inactive"}
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-4">
                        <button
                          onClick={() => handleEdit(table._id || "")}
                          className="text-blue-500 hover:text-blue-700"
                        >
                          <Edit
                            id="edit-icon"
                            data-tooltip-id="edit-tooltip"
                            data-tooltip-content="Edit"
                            size={20}
                            className="cursor-pointer text-blue-500 hover:text-blue-700 transition-colors"
                          />
                          <Tooltip
                            id="edit-tooltip"
                            place="bottom"
                            className="!rounded-lg !bg-gray-800 !text-white !text-xs !px-3 !py-1 !shadow-lg"
                          />
                        </button>
                        <button
                          onClick={() => handleDelete(table._id || "")}
                          className="text-red-400 hover:text-red-600"
                        >
                          <Trash2
                            id="delete-icon"
                            data-tooltip-id="delete-tooltip"
                            data-tooltip-content="Delete"
                            size={20}
                            className="cursor-pointer text-red-500 hover:text-red-700 transition-colors"
                          />
                          <Tooltip
                            id="delete-tooltip"
                            place="bottom"
                            className="!rounded-lg !bg-gray-800 !text-white !text-xs !px-2 !py-1 !shadow-lg"
                          />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Pagination */}
      {!isLoading && !error && filteredTables.length > 0 && (
        <div className="px-5 py-5 flex flex-col lg:flex-row md:items-start items-center justify-between">
          <div className="flex items-center">
            <button
              onClick={() => handlePageChange(currentPage - 1)}
              className="h-8 w-8 mr-1 flex justify-center items-center rounded-md border border-gray-300 bg-gray-100 text-gray-500 hover:bg-gray-100 disabled:opacity-50"
              disabled={currentPage === 1}
            >
              <svg
                className="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>

            {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
              let pageToShow = i + 1;
              if (totalPages > 5 && currentPage > 3) {
                pageToShow = currentPage - 2 + i;
                if (pageToShow > totalPages) {
                  pageToShow = totalPages - (4 - i);
                }
              }

              return (
                <button
                  key={i}
                  onClick={() => handlePageChange(pageToShow)}
                  className={`h-8 w-8 mx-1 flex justify-center items-center rounded-md ${
                    currentPage === pageToShow
                      ? "border border-orange-500 bg-orange-500 text-white"
                      : "border border-gray-300 bg-gray-100 text-gray-500 hover:bg-gray-100"
                  }`}
                >
                  {pageToShow}
                </button>
              );
            })}

            <button
              onClick={() => handlePageChange(currentPage + 1)}
              className="h-8 w-8 ml-1 flex justify-center items-center rounded-md border border-gray-300 bg-gray-100 text-gray-500 hover:bg-gray-100 disabled:opacity-50"
              disabled={currentPage === totalPages}
            >
              <svg
                className="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>
          </div>
          <div className="text-sm text-gray-600">
            Showing {indexOfFirstTable + 1} to{" "}
            {Math.min(indexOfLastTable, filteredTables.length)} of{" "}
            {filteredTables.length} tables
          </div>
        </div>
      )}
    </>
  );
};

export default AdminTables;
