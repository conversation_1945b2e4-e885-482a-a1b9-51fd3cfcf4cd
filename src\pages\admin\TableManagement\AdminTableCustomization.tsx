import React, { useState, useRef, useMemo, useEffect } from 'react';
import { Plus, Save, RotateCcw } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import {
  useGetSitesQuery,
} from "../../../store/api/siteManagementApi";
import {
  useGetTablesQuery,
  useUpdateTablePositionsMutation
} from "../../../store/api/TableManagementApi";
import Swal from 'sweetalert2';

interface LocationData {
  _id: string;
  [key: string]: any;
}

interface TableData {
  _id: string;
  tableNo: string;
  tableName: string;
  x: number;
  y: number;
  floor: string;
  capacity: number;
  location: string | LocationData;
}


const TablesCustomization: React.FC = () => {
  const [activeFloor, setActiveFloor] = useState<string | null>(null);
  const [tables, setTables] = useState<TableData[]>([]);
  const [originalTables, setOriginalTables] = useState<TableData[]>([]);
  const [isCustomizing, setIsCustomizing] = useState<boolean>(false);
  const [draggedTable, setDraggedTable] = useState<string | null>(null);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [selectedSite, setSelectedSite] = useState<string | null>(null);
  const canvasRef = useRef<HTMLDivElement>(null);
  
  const userId = localStorage.getItem('userId') || '';
  
  const { data: sitesData = [], isLoading: sitesLoading } = useGetSitesQuery(userId);
  const { data: tablesData = [], isLoading: tablesLoading, refetch: refetchTables } = useGetTablesQuery(userId);
  const [updateTablePositions, { isLoading: isUpdating }] = useUpdateTablePositionsMutation();

  // Helper function to create placeholder tables for a site with no tables
  const generatePlaceholderTables = (siteId: string, count: number): TableData[] => {
    // If count is not a valid number, default to 4
    const tableCount = !isNaN(Number(count)) ? Number(count) : 4;
    
    const spacing = 100; // Space between tables
    const startX = 150; // Starting X position
    const startY = 150; // Starting Y position
    const tables: TableData[] = [];
    
    // Create tables in a grid arrangement
    const perRow = Math.ceil(Math.sqrt(tableCount)); // Tables per row
    
    for (let i = 0; i < tableCount; i++) {
      const row = Math.floor(i / perRow);
      const col = i % perRow;
      
      tables.push({
        _id: `placeholder-${siteId}-${i}`,
        tableNo: `${i + 1}`,
        tableName: `Table ${i + 1}`,
        x: startX + (col * spacing),
        y: startY + (row * spacing),
        floor: siteId,
        capacity: 4,
        location: siteId
      });
    }
    
    return tables;
  };

  // Set default site when sites are loaded
  useEffect(() => {
    if (sitesData.length > 0) {
      // Set default selected site if not already set
      if (!selectedSite) {
        setSelectedSite(sitesData[0]?._id || null);
      }
      
      // Set default active floor to the first site's ID
      if (!activeFloor && sitesData[0]?._id) {
        setActiveFloor(sitesData[0]?._id);
      }
    }
  }, [sitesData, selectedSite, activeFloor]);

  // Transform tables data when it's loaded or when selected site changes
  useEffect(() => {
    if (selectedSite && sitesData.length > 0) {
      const currentSite = sitesData.find(s => s._id === selectedSite);
      const expectedTableCount = currentSite?.numberOfTables || "0";
      
      // Filter tables for the selected site
      let siteTablesData: TableData[] = [];
      
      if (tablesData.length > 0) {
        siteTablesData = tablesData
          .filter(table => {
            // Filter tables based on location matching selected site
            if (typeof table.location === 'object' && table.location && '_id' in table.location) {
              return table.location._id === selectedSite;
            } else if (typeof table.location === 'string') {
              return table.location === selectedSite;
            }
            return false;
          })
          .map(table => ({
            _id: table._id || '',
            tableNo: table.tableNo || '',
            tableName: table.tableName || '',
            x: table.x || 200,
            y: table.y || 200,
            floor: selectedSite, // Set floor to selectedSite for consistency
            capacity: table.capacity || 4,
            location: typeof table.location === 'string' ? table.location : (table.location as LocationData)
          }));
      }
      
      // If there are no tables but numberOfTables > 0, generate placeholders
      if (siteTablesData.length === 0 && Number(expectedTableCount) > 0) {
        // Add placeholder tables (these will be shown in read-only mode)
        const placeholders = generatePlaceholderTables(selectedSite, Number(expectedTableCount));
        siteTablesData = placeholders;
      }
      
      setTables(siteTablesData);
      setOriginalTables([...siteTablesData]);
    }
  }, [tablesData, selectedSite, sitesData]);

  // Filter tables based on active floor
  const filteredTables = useMemo(() => 
    tables.filter(table => table.floor === activeFloor), 
    [tables, activeFloor]
  );

  const handleFloorClick = (siteId: string) => {
    setActiveFloor(siteId);
    setSelectedSite(siteId); // Update selectedSite to match activeFloor
  };
 
  const handleMouseDown = (e: React.MouseEvent, tableId: string) => {
    // Only allow dragging if in customizing mode
    if (!isCustomizing) return;
    
    if (canvasRef.current) {
      const rect = canvasRef.current.getBoundingClientRect();
      const table = tables.find(t => t._id === tableId);
      
      if (table) {
        setDraggedTable(tableId);
        setDragOffset({ 
          x: e.clientX - (table.x + rect.left), 
          y: e.clientY - (table.y + rect.top)
        });
      }
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (draggedTable !== null && canvasRef.current && isCustomizing) {
      const rect = canvasRef.current.getBoundingClientRect();
      
      // Adjust boundaries based on table size (80x80)
      const newX = Math.max(40, Math.min(rect.width - 40, e.clientX - rect.left - dragOffset.x));
      const newY = Math.max(40, Math.min(rect.height - 40, e.clientY - rect.top - dragOffset.y));
      
      setTables(tables.map(table => 
        table._id === draggedTable 
          ? { ...table, x: newX, y: newY } 
          : table
      ));
    }
  };

  const handleMouseUp = () => {
    setDraggedTable(null);
  };

  const navigate = useNavigate();

  const handleAddTable = () => {
    // If in customization mode, save changes instead
    if (isCustomizing) {
      saveChanges();
    } else {
      navigate('/admin/tables-management/tables/table-form');
    }
  };

  const toggleCustomization = () => {
    if (!isCustomizing) {
      // Start customization - store original table positions
      setOriginalTables([...tables]);
      setIsCustomizing(true);
    } else {
      // Reset changes
      resetChanges();
    }
  };

  const resetChanges = () => {
    // Restore original table positions
    setTables([...originalTables]);
    setIsCustomizing(false);
  };

  const saveChanges = async () => {
    if (!selectedSite) {
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'No site selected',
        confirmButtonColor: '#d33'
      });
      return;
    }

    try {
      // Include all tables (including placeholders) in the save
      const tablePositions = tables.map(table => ({
        tableNo: table.tableNo,
        x: table.x,
        y: table.y,
        floor: table.floor
      }));

      // Log what we're sending to help debug
      console.log("Saving table positions:", {
        siteId: selectedSite,
        tablePositions
      });

      // Call API to update positions
      await updateTablePositions({
        siteId: selectedSite,
        tablePositions
      }).unwrap();

      // Update originalTables to current positions so they become the new baseline
      setOriginalTables([...tables]);
      setIsCustomizing(false);
   
Swal.fire({
  icon: 'success',
  title: 'Success',
  text: 'Table positions saved successfully',
  confirmButtonColor: '#3085d6'
});
      
      // Refresh tables data
      refetchTables();
    } catch (error) {
      console.error("Failed to save table positions:", error);
     
Swal.fire({
  icon: 'error',
  title: 'Error',
  text: 'Failed to save table positions',
  confirmButtonColor: '#d33'
});
    }
  };

  // Updated function to render rectangular seats around the table
  const renderSeats = (capacity: number) => {
    const seats = [];
    
    // Render seats based on capacity
    if (capacity === 2) {
      // 2 seats - top and bottom
      seats.push(
        <div key="top" className="absolute w-16 h-3 bg-white border border-gray-300 rounded-md -top-4 left-1/2 transform -translate-x-1/2" />,
        <div key="bottom" className="absolute w-16 h-3 bg-white border border-gray-300 rounded-md -bottom-4 left-1/2 transform -translate-x-1/2" />
      );
    } else if (capacity === 4) {
      // 4 seats - one on each side
      seats.push(
        <div key="top" className="absolute w-16 h-3 bg-white border border-gray-300 rounded-md -top-4 left-1/2 transform -translate-x-1/2" />,
        <div key="right" className="absolute w-3 h-16 bg-white border border-gray-300 rounded-md top-1/2 -right-4 transform -translate-y-1/2" />,
        <div key="bottom" className="absolute w-16 h-3 bg-white border border-gray-300 rounded-md -bottom-4 left-1/2 transform -translate-x-1/2" />,
        <div key="left" className="absolute w-3 h-16 bg-white border border-gray-300 rounded-md top-1/2 -left-4 transform -translate-y-1/2" />
      );
    } else if (capacity === 6) {
      // 6 seats - two on top, two on bottom, one on each side
      seats.push(
        <div key="top1" className="absolute w-12 h-3 bg-white border border-gray-300 rounded-md -top-4 left-4 transform -translate-x-1/2" />,
        <div key="top2" className="absolute w-12 h-3 bg-white border border-gray-300 rounded-md -top-4 right-4 transform translate-x-1/2" />,
        <div key="right" className="absolute w-3 h-16 bg-white border border-gray-300 rounded-md top-1/2 -right-4 transform -translate-y-1/2" />,
        <div key="bottom1" className="absolute w-12 h-3 bg-white border border-gray-300 rounded-md -bottom-4 left-4 transform -translate-x-1/2" />,
        <div key="bottom2" className="absolute w-12 h-3 bg-white border border-gray-300 rounded-md -bottom-4 right-4 transform translate-x-1/2" />,
        <div key="left" className="absolute w-3 h-16 bg-white border border-gray-300 rounded-md top-1/2 -left-4 transform -translate-y-1/2" />
      );
    } else if (capacity === 8) {
      // 8 seats - two on each side
      seats.push(
        // Top row
        <div key="top1" className="absolute w-12 h-3 bg-white border border-gray-300 rounded-md -top-4 left-4 transform -translate-x-1/2" />,
        <div key="top2" className="absolute w-12 h-3 bg-white border border-gray-300 rounded-md -top-4 right-4 transform translate-x-1/2" />,
        
        // Right column
        <div key="right1" className="absolute w-3 h-10 bg-white border border-gray-300 rounded-md top-4 -right-4 transform -translate-y-1/2" />,
        <div key="right2" className="absolute w-3 h-10 bg-white border border-gray-300 rounded-md bottom-4 -right-4 transform translate-y-1/2" />,
        
        // Bottom row
        <div key="bottom1" className="absolute w-12 h-3 bg-white border border-gray-300 rounded-md -bottom-4 left-4 transform -translate-x-1/2" />,
        <div key="bottom2" className="absolute w-12 h-3 bg-white border border-gray-300 rounded-md -bottom-4 right-4 transform translate-x-1/2" />,
        
        // Left column
        <div key="left1" className="absolute w-3 h-10 bg-white border border-gray-300 rounded-md top-4 -left-4 transform -translate-y-1/2" />,
        <div key="left2" className="absolute w-3 h-10 bg-white border border-gray-300 rounded-md bottom-4 -left-4 transform translate-y-1/2" />
      );
    }
    
    return seats;
  };

  // Loading state
  if (tablesLoading || sitesLoading) {
    return (
      <div className="bg-gray-50 p-4 shadow min-h-screen flex justify-center items-center">
        <div className="flex items-center space-x-2">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-orange-500"></div>
          <span>Loading site customization...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 w-full bg-gray-50">
      <div className="flex md:flex-row flex-col justify-between items-center mb-6 border border-gray-200 p-2 rounded-2xl bg-white">
        <h1 className="text-3xl font-bold text-gray-800 p-3">Tables Customization</h1>
        
        <div className="flex space-x-2">
          <button 
            className={`border px-4 py-2 rounded-md flex items-center ${
              isCustomizing
                ? 'bg-white border-red-500 hover:bg-red-500 hover:text-white text-red-500'
                : 'bg-white border-orange-500 hover:bg-orange-500 hover:text-white text-orange-500'
            }`}
            onClick={toggleCustomization}
          >
            {isCustomizing ? (
              <>Reset Changes <RotateCcw size={16} className="ml-1" /></>
            ) : (
              <>Customize Tables <Plus size={16} className="ml-1" /></>
            )}
          </button>
          <button 
            className="bg-orange-500 text-white px-4 py-2 rounded-md flex items-center"
            onClick={handleAddTable}
            disabled={isUpdating}
          >
            {isCustomizing ? (
              <>Save Changes <Save size={16} className="ml-1" /></>
            ) : (
              <>Add Table <Plus size={16} className="ml-1" /></>
            )}
          </button>
        </div>
      </div>
      
      {/* Replace the hardcoded floors with sitesData */}
      <div className="mb-6 flex space-x-2 flex-wrap">
        {sitesData.map((site) => (
          <button
            key={site._id}
            className={`px-4 py-1 rounded-xl mb-2 ${
              activeFloor === site._id 
                ? 'bg-orange-100 text-orange-600 border border-orange-500' 
                : 'text-gray-700 hover:bg-gray-100'
            }`}
            onClick={() => site._id && handleFloorClick(site._id)}
          >
            {site.siteName}
          </button>
        ))}
      </div>
      
      <div 
        ref={canvasRef}
        className={`bg-white rounded-md border border-gray-200 h-96 relative overflow-hidden ${
          isCustomizing ? 'cursor-move' : ''
        }`}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      >
        {filteredTables.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full">
            <div className="text-lg mb-4 text-gray-700 font-medium">
              {selectedSite 
                ? sitesData.find(s => s._id === activeFloor)?.siteName || 'Selected Site'
                : 'Please select a site to view tables'
              }
            </div>
            {selectedSite && (
              <div className="text-center">
                <div className="mb-4">
                  <div className="text-sm text-gray-600">Expected Tables</div>
                  <div className="text-2xl font-bold text-orange-500">
                    {sitesData.find(s => s._id === activeFloor)?.numberOfTables || '0'}
                  </div>
                </div>
                <div className="mb-4">
                  <div className="text-sm text-gray-600">Current Tables</div>
                  <div className="text-2xl font-bold text-gray-700">0</div>
                </div>
                <button
                  onClick={() => navigate('/admin/tables-management/tables/table-form')}
                  className="mt-2 px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 transition-colors"
                >
                  Add Tables
                </button>
              </div>
            )}
          </div>
        ) : (
          filteredTables.map(table => (
            <div 
              key={table._id}
              className="absolute"
              style={{ 
                left: `${table.x - 40}px`, 
                top: `${table.y - 40}px`,
                cursor: isCustomizing ? (draggedTable === table._id ? 'grabbing' : 'grab') : 'default'
              }}
            >
              <div className="relative w-20 h-20">
                <div className="w-20 h-20 border-2 border-gray-300 rounded-md flex items-center justify-center">
                  <div 
                    className={`w-16 h-16 rounded-md flex items-center justify-center bg-orange-100 ${isCustomizing ? 'hover:bg-orange-200' : ''}`}
                    onMouseDown={(e) => handleMouseDown(e, table._id)}
                  >
                    <span className="text-gray-800 font-medium">{table.tableNo}</span>
                  </div>
                </div>
                
                {/* Render rectangular seats around the table */}
                {renderSeats(table.capacity || 4)}
              </div>
            </div>
          ))
        )}
      </div>
      
      <div className="mt-4 bg-white p-3 rounded-md border border-gray-200">
        <p className="text-gray-700 text-sm">
          <span className="font-medium">Current site:</span> {sitesData.find(s => s._id === activeFloor)?.siteName || 'None'} 
          <span className="ml-4 font-medium">Tables:</span> {filteredTables.length}
          <span className="ml-4 font-medium">Expected Tables:</span> {sitesData.find(s => s._id === activeFloor)?.numberOfTables || '0'}
          {isCustomizing && <span className="ml-4 text-orange-500 font-medium">Customization mode active</span>}
        </p>
      </div>
    </div>
  );
};

export default TablesCustomization;
