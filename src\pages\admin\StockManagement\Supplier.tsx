import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  Search,
  Edit,
  Trash2,
  ChevronLeft,
  ChevronRight,
  Plus,
} from "lucide-react";
import {
  useGetSuppliersQuery,
  useDeleteSupplierMutation,
} from "../../../store/api/supplierApi";
import { toast } from 'react-toastify';

export default function Supplier() {
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(5);
  const [showModal, setShowModal] = useState(false);
  const [supplierToDelete, setSupplierToDelete] = useState<string | null>(null);

  const navigate = useNavigate();
  const userId = localStorage.getItem('userId') || '';

  const { data: suppliersData, isLoading, error, refetch } = useGetSuppliersQuery(userId);
  const [deleteSupplier, { isLoading: isDeleting }] = useDeleteSupplierMutation();

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  const handleDelete = (id: string) => {
    setSupplierToDelete(id);
    setShowModal(true);
  };

  const confirmDelete = async () => {
    if (supplierToDelete) {
      try {
        await deleteSupplier(supplierToDelete).unwrap();
        toast.success('Supplier deleted successfully');
        refetch();
        setShowModal(false);
      } catch (error) {
        console.error('Failed to delete supplier:', error);
        toast.error('Failed to delete supplier');
        setShowModal(false);
      }
    }
  };

  const handleAddSupplier = () => {
    navigate('/admin/stock-management/supplier/supplier-form');
  };

  const handleEdit = (id: string) => {
    navigate(`/admin/stock-management/supplier/supplier-form/${id}`);
  };

  // Filter suppliers based on search term
  const filteredSuppliers = suppliersData?.filter(supplier =>
    supplier.SupplierName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    supplier.SupplierID.toLowerCase().includes(searchTerm.toLowerCase()) ||
    supplier.ContactNumber.includes(searchTerm)
  ) || [];

  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentSuppliers = filteredSuppliers.slice(indexOfFirstItem, indexOfLastItem);

  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);

  useEffect(() => {
    if (error) {
      toast.error('Failed to fetch suppliers');
      console.error('Error fetching suppliers:', error);
    }
  }, [error]);

  return (
    <div className="p-4 bg-gray-50">
      {/* Delete Confirmation Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black/50 flex justify-center items-center z-50">
          <div className="bg-white p-8 rounded-xl shadow-xl w-full max-w-md text-center relative">
            <div className="flex justify-center mb-4">
              <div className="bg-orange-100 text-orange-500 p-4 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01M12 5c.512 0 1.023.195 1.414.586C13.805 6.977 14 7.488 14 8s-.195 1.023-.586 1.414A1.993 1.993 0 0112 10a1.993 1.993 0 01-1.414-.586A1.993 1.993 0 0110 8c0-.512.195-1.023.586-1.414A1.993 1.993 0 0112 5z" />
                </svg>
              </div>
            </div>
            <h3 className="text-2xl font-semibold text-gray-800">Are you sure?</h3>
            <p className="mt-2 text-gray-600">Do you want to delete this Supplier?</p>
            <div className="mt-6 flex justify-center gap-4">
              <button
                onClick={confirmDelete}
                className="bg-orange-600 hover:bg-orange-700 text-white px-5 py-2 rounded-md font-medium"
              >
                Yes, delete it!
              </button>
              <button
                onClick={() => setShowModal(false)}
                className="bg-gray-200 hover:bg-gray-300 text-gray-800 px-5 py-2 rounded-md font-medium"
              >
                No, cancel!
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="flex justify-between md:flex-row flex-col items-center p-2 border border-gray-50 bg-white mb-2 rounded-2xl">
        <h1 className="text-3xl p-3 font-bold text-gray-800">Supplier Details</h1>
        <div className="flex gap-2 md:flex-row flex-col-reverse">
          <div className="relative">
            <input
              type="text"
              placeholder="Search Supplier"
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg w-64 focus:outline-none focus:ring-2 focus:ring-orange-500"
              value={searchTerm}
              onChange={handleSearch}
            />
            <Search className="absolute left-3 top-2.5 text-gray-400 h-5 w-5" />
          </div>
          <button
            onClick={handleAddSupplier}
            className="bg-orange-500 hover:bg-orange-600 text-white font-medium px-4 py-2 rounded-lg flex items-center gap-1"
          >
            Add New Supplier
            <Plus size={20} />
          </button>
        </div>
      </div>

      {isLoading ? (
        <p className="text-center text-gray-600 py-8">Loading suppliers...</p>
      ) : error ? (
        <p className="text-center text-red-500 py-8">Failed to load suppliers.</p>
      ) : (
        <>
          <div className="bg-white shadow-sm border border-gray-200 rounded-2xl">
            <div className="overflow-x-auto rounded-2xl">
              <table className="w-full">
                <thead className="bg-orange-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-sm font-medium text-gray-700">
                      Supplier ID
                    </th>
                    <th className="px-6 py-3 text-left text-sm font-medium text-gray-700">
                      Supplier Name
                    </th>
                    <th className="px-6 py-3 text-left text-sm font-medium text-gray-700">
                      Contact
                    </th>
                    <th className="px-6 py-3 text-left text-sm font-medium text-gray-700">
                      Email
                    </th>
                    <th className="px-6 py-3 text-left text-sm font-medium text-gray-700">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white">
                  {currentSuppliers.length > 0 ? (
                    currentSuppliers.map((supplier) => (
                      <tr key={supplier.id} className="hover:bg-orange-50">
                        <td className="px-6 py-4 text-sm text-gray-800">
                          {supplier.SupplierID}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-800">
                          {supplier.SupplierName}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-800">
                          {supplier.ContactNumber}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-800">
                          {supplier.EmailAddress}
                        </td>
                        <td className="px-6 py-4 text-sm flex gap-2">
                          <button
                            onClick={() => handleEdit(supplier.id)}
                            className="text-blue-500 hover:text-blue-700"
                            disabled={isDeleting}
                          >
                            <Edit size={18} />
                          </button>
                          <button
                            onClick={() => handleDelete(supplier.id)}
                            className={`text-red-500 hover:text-red-700 ${isDeleting ? "cursor-not-allowed opacity-50" : ""}`}
                            disabled={isDeleting}
                          >
                            <Trash2 size={18} />
                          </button>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={5} className="px-6 py-4 text-center text-gray-500">
                        No suppliers found.
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
          
          {/* Pagination */}
          {filteredSuppliers.length > 0 && (
            <div className="flex justify-start items-center space-x-2 p-4">
              <button
                className={`p-1 rounded-md border border-gray-200 ${
                  currentPage === 1
                    ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                    : "hover:bg-gray-100"
                }`}
                onClick={() => paginate(Math.max(currentPage - 1, 1))}
                disabled={currentPage === 1}
              >
                <ChevronLeft size={18} className="text-gray-600" />
              </button>

              {Array.from(
                { length: Math.ceil(filteredSuppliers.length / itemsPerPage) },
                (_, i) => i + 1
              ).map((number) => (
                <button
                  key={number}
                  onClick={() => paginate(number)}
                  className={`w-8 h-8 rounded-md flex items-center justify-center border ${
                    currentPage === number
                      ? "bg-orange-500 text-white"
                      : "bg-white text-gray-700 hover:bg-gray-50"
                  }`}
                >
                  {number}
                </button>
              ))}

              <button
                className={`p-1 rounded-md border border-gray-200 ${
                  indexOfLastItem >= filteredSuppliers.length
                    ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                    : "hover:bg-gray-100"
                }`}
                onClick={() => paginate(currentPage + 1)}
                disabled={indexOfLastItem >= filteredSuppliers.length}
              >
                <ChevronRight size={18} className="text-gray-600" />
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
}