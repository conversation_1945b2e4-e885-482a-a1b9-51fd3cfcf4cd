import React from 'react';

interface OutOfStockAlertProps {
  isOpen: boolean;
  onClose: () => void;
  productName: string;
}

const OutOfStockAlert: React.FC<OutOfStockAlertProps> = ({ isOpen, onClose, productName }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50">
      <div className="fixed inset-0 bg-gray-400 opacity-30 " onClick={onClose}></div>
      <div className="bg-white rounded-lg  shadow-xl w-full max-w-md mx-4 z-10">
        <div className="bg-orange px-6 py-4 flex items-center justify-between">
          <h3 className="text-xl font-bold text-white">Out of Stock</h3>
          <button onClick={onClose} className="text-white cursor-pointer font-bold text-3xl">×</button>
        </div>
        <div className="px-6 py-8 text-center">
          <div className="mb-6 bg-red-100 p-3 rounded-full inline-block">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-orange" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h4 className="text-xl font-semibold mb-2">Product Unavailable</h4>
          <p className="text-gray-600 mb-4">
            Sorry, <span className="font-semibold">{productName}</span> is currently out of stock.
          </p>
        </div>
        <div className="bg-gray-50 px-6 py-4 flex justify-center">
          <button
            onClick={onClose}
            className="bg-orange cursor-pointer hover:bg-orange-600 text-white font-medium py-2 px-6 rounded-md"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default OutOfStockAlert;