import React, { useEffect, useRef, useState } from "react";
import CustomModal from "../../CustomModal";
import { DateRangePicker, type RangeKeyDict } from "react-date-range";
import "react-date-range/dist/styles.css";
import "react-date-range/dist/theme/default.css";
import { format, startOfWeek, endOfWeek, startOfMonth, endOfMonth, isWithinInterval } from "date-fns";
import { FiSearch } from "react-icons/fi";
import { useGetEmployeeTimeQuery } from "../../../store/api/pos/customer";
import EmployeeMonthlyReport from "./EmployeeMonthlyReport";
import AttendanceRecordModal from "./AttendanceRecordModal";

interface EmployeeAccessRecordProps {
  isOpen: boolean;
  onClose: () => void;
}

interface EmployeeRecord {
  empName: string;
  startDate: string;
  endDate?: string;
  startHour: string;
  endHour?: string;
  employeeId?: string;
  address?: string;
  email?: string;
  employeeEndTime?: string;
  employeeStartTime?: string;
  employeeType?: string;
  firstName?: string;
  lastName?: string;
  hourlyRate?: number;
  overTimeRate?: number;
  phoneNo?: string;
  role?: string;
  totalHours?: number;
  userId?: string;
  _id?: string;
  // Additional fields that might be in the data
  [key: string]: any;
}

const EmployeeAccessRecord: React.FC<EmployeeAccessRecordProps> = ({
  isOpen,
  onClose,
}) => {
  const [reportType, setReportType] = useState<string>("");
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const userId = localStorage.getItem("userId") || "";

  // Use the original RTK Query hook with the userId parameter
  const { data, isLoading, error } = useGetEmployeeTimeQuery(userId);

  // State to control the EmployeeMonthlyReport modal
  const [isMonthlyReportOpen, setIsMonthlyReportOpen] = useState(false);

  // State to control the AttendanceRecordModal
  const [isAttendanceModalOpen, setIsAttendanceModalOpen] = useState(false);

  // State to store filtered data for the EmployeeMonthlyReport
  const [filteredDataForReport, setFilteredDataForReport] = useState<any[]>([]);

  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([
    new Date(),
    new Date(),
  ]);
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [Records, setRecords] = useState<EmployeeRecord[]>([]);
  const calendarRef = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [startDate, endDate] = dateRange;
  const [currentPage, setCurrentPage] = useState(1);

  // Number of records to display per page
  const recordsPerPage = 5;

  useEffect(() => {
    if (data) {
      console.log("API data:", data);
      setRecords(data);

      // Initially filter data based on the current report type if one is selected
      if (reportType) {
        const initialFilteredData = filterDataByReportType(reportType);
        setFilteredDataForReport(initialFilteredData);
      }
    } else {
      // Fallback data in case API doesn't return anything
      const fallbackData = [
        {
          empName: "Asad Khan",
          startDate: "2025-03-12T14:04:00Z",
          startHour: "2:04 PM",
          employeeId: "443343",
          address: "Florida",
          email: "<EMAIL>",
          employeeEndTime: "23:40",
          employeeStartTime: "15:10",
          employeeType: "manager",
          firstName: "Asad",
          lastName: "Khan",
          hourlyRate: 4,
          overTimeRate: 2,
          phoneNo: "3523452345",
          role: "employee",
          totalHours: 8,
          userId: "65d6e2acf4cb2c368afded71",
          _id: "67befd9a77d88866393c1b26"
        },
        {
          empName: "Asad Khan",
          startDate: "2025-03-12T14:24:08Z",
          startHour: "2:24:08 PM",
          employeeId: "443343",
          address: "Florida",
          email: "<EMAIL>",
          employeeEndTime: "23:40",
          employeeStartTime: "15:10",
          employeeType: "manager",
          firstName: "Asad",
          lastName: "Khan",
          hourlyRate: 4,
          overTimeRate: 2,
          phoneNo: "3523452345",
          role: "employee",
          totalHours: 8,
          userId: "65d6e2acf4cb2c368afded71",
          _id: "67befd9a77d88866393c1b26"
        },
        // ... other fallback records
      ];

      setRecords(fallbackData);

      // Also set filtered data for the report using fallback data if a report type is selected
      if (reportType) {
        const transformedFallbackData = fallbackData.map(record => transformRecordForReport(record));
        setFilteredDataForReport(transformedFallbackData);
      }
    }
  }, [data, reportType]);

  // Filter records based on search term
  const filteredRecords = Records.filter(record =>
    record.empName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Transform a single record for the report
  const transformRecordForReport = (employee: any) => {
    // Calculate shift hours based on total hours
    const hours = Math.floor(employee.totalHours || 0);
    const minutes = Math.floor((employee.totalHours % 1) * 60) || 0;
    const seconds = Math.floor(((employee.totalHours % 1) * 60) % 1 * 60) || 0;

    // Calculate shift total amount based on hourly rate and total hours
    const shiftTotalAmount = (employee.hourlyRate || 0) * (employee.totalHours || 0);

    // Get month from start date
    const monthNames = ["January", "February", "March", "April", "May", "June",
      "July", "August", "September", "October", "November", "December"];
    const date = new Date(employee.startDate);
    const month = monthNames[date.getMonth()] + " " + date.getFullYear();

    // Create a properly formatted record for the EmployeeMonthlyReport
    return {
      ...employee,
      empName: employee.empName || `${employee.firstName || ''} ${employee.lastName || ''}`.trim(),
      shiftHours: `${hours} hr, ${minutes} mm, ${seconds}ss`,
      shiftTotalAmount: parseFloat(shiftTotalAmount.toFixed(2)),
      month: month,
      startHourlyRate: employee.hourlyRate,
      deductions: 0, // Default value
      total: parseFloat(shiftTotalAmount.toFixed(2)) // Same as shiftTotalAmount if no deductions
    };
  };

  // Function to filter and transform data based on report type
  const filterDataByReportType = (type: string) => {
    if (!data || !Array.isArray(data)) {
      // If no API data, use the Records state
      if (Records.length > 0) {
        return Records.map(record => transformRecordForReport(record));
      }
      return [];
    }

    const today = new Date();

    // First filter the data based on the report type
    const filteredData = data.filter(record => {
      if (!record.startDate) return false;

      const recordDate = new Date(record.startDate);

      switch (type) {
        case "Daily":
          // Filter records from today
          return (
            recordDate.getDate() === today.getDate() &&
            recordDate.getMonth() === today.getMonth() &&
            recordDate.getFullYear() === today.getFullYear()
          );

        case "Weekly":
          // Filter records from the current week
          const weekStart = startOfWeek(today);
          const weekEnd = endOfWeek(today);
          return isWithinInterval(recordDate, { start: weekStart, end: weekEnd });

        case "Monthly":
          // Filter records from the current month
          const monthStart = startOfMonth(today);
          const monthEnd = endOfMonth(today);
          return isWithinInterval(recordDate, { start: monthStart, end: monthEnd });

        default:
          return true;
      }
    });

    console.log(`Filtered ${type} data:`, filteredData);

    // Then transform the filtered data to match the format expected by EmployeeMonthlyReport
    return filteredData.map(employee => transformRecordForReport(employee));
  };

  // Calculate total number of pages
  const totalPages = Math.ceil(filteredRecords.length / recordsPerPage);

  // Get current records for the page
  const getCurrentRecords = () => {
    const startIndex = (currentPage - 1) * recordsPerPage;
    const endIndex = startIndex + recordsPerPage;
    return filteredRecords.slice(startIndex, endIndex);
  };

  // Handle clicks outside of calendar to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        calendarRef.current &&
        !calendarRef.current.contains(event.target as Node)
      ) {
        setIsCalendarOpen(false);
      }
      
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };

    if (isCalendarOpen || isDropdownOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      return () => document.removeEventListener("mousedown", handleClickOutside);
    }
  }, [isCalendarOpen, isDropdownOpen]);

  const handleDateRangeChange = (ranges: RangeKeyDict) => {
    const selection = ranges.selection;
    if (selection) {
      setDateRange([selection.startDate || null, selection.endDate || null]);
    }
  };

  const formatDateRange = () => {
    if (!startDate || !endDate) return "Select Date Range";
    return `${format(startDate, "MMM dd, yyyy ")} → ${format(
      endDate,
      "MMM dd, yyyy"
    )}`;
  };

  const handleAttendance = () => {
    console.log("Attendance button clicked");
    setIsAttendanceModalOpen(true);
  };

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1); // Reset to first page when searching
  };

  // Handle search button click
  const handleSearch = () => {
    // The filtering is already happening reactively through the filteredRecords variable
    // This function is here in case you want to add additional search functionality
    console.log("Searching for:", searchTerm);
  };

  // Handle report type change - Just update state and filter data, don't open modal
  const handleReportTypeChange = (newReportType: string) => {
    console.log("Changing report type to:", newReportType);
    setReportType(newReportType);
    setCurrentPage(1); // Reset to first page when changing report type

    // Filter data based on the selected report type
    const filteredData = filterDataByReportType(newReportType);
    console.log(`Filtered data for ${newReportType} report:`, filteredData);
    setFilteredDataForReport(filteredData);

    // Open the monthly report modal when report type changes
    setIsMonthlyReportOpen(true);
  };

  // Open the report modal with the filtered data
  const handleOpenMonthlyReport = () => {
    console.log("Opening EmployeeMonthlyReport modal");
    // Filter data based on the current report type
    const filteredData = filterDataByReportType(reportType);
    setFilteredDataForReport(filteredData);
    setIsMonthlyReportOpen(true);
  };

  // Handle next page
  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  // Handle previous page
  const handlePrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const footer = (
    <div className="flex justify-between items-center p-2">
      <div className="flex items-center gap-2 border-1 border-gray-300 rounded-2xl">
        <button
          className={`px-4 py-2 text-sm font-medium ${currentPage === 1
            ? "text-gray-400 cursor-not-allowed"
            : "text-[#9C9C9C] cursor-pointer"
            }`}
          onClick={handlePrevPage}
          disabled={currentPage === 1}
          type="button"
        >
          ← Previous
        </button>
        <span className="w-12 text-center text-black border-x border-gray-300 rounded-lg p-2">
          {currentPage}
        </span>
        <button
          className={`px-4 py-2 text-sm font-medium ${currentPage >= totalPages
            ? "text-gray-400 cursor-not-allowed"
            : "text-[#9C9C9C] cursor-pointer"
            }`}
          onClick={handleNextPage}
          disabled={currentPage >= totalPages}
          type="button"
        >
          Next →
        </button>
      </div>
      <div className="flex gap-4">

        <button
          onClick={handleAttendance}
          className="px-8 py-2 bg-orange text-white rounded-full cursor-pointer transition-colors"
          type="button"
        >
          Attendance
        </button>
        <button
          onClick={onClose}
          className="px-14 py-2 border border-orange text-orange text-lg font-poppins font-semibold rounded-full cursor-pointer transition-colors"
          type="button"
        >
          Cancel
        </button>
      </div>
    </div>
  );

  function formatDate(isoDate: string): string {
    if (!isoDate) return "";
    try {
      const date = new Date(isoDate);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      });
    } catch (e) {
      return isoDate; // Return the original string if parsing fails
    }
  }

  // Handle row click
  const handleRowClick = (record: EmployeeRecord) => {
    console.log("Row clicked:", record);
    // Filter data based on the current report type
    const filteredData = filterDataByReportType(reportType);
    console.log(`Row click: Filtered data for ${reportType} report:`, filteredData);
    setFilteredDataForReport(filteredData);

    // Open the report modal with the filtered data
    handleOpenMonthlyReport();
  };

  return (
    <>
      {/* Main Employee Access Record Modal */}
      <CustomModal
        isOpen={isOpen}
        onClose={onClose}
        title={`Employees  Access Record`}
        width="max-w-6xl"
        zIndex={40} // Set a lower z-index for the main modal
        footer={footer}
      >
        <div className="p-6">
          {/* Search and Filters */}
          <div className="flex justify-between items-center mb-6 pb-3 border-b border-[#E4E4E4]">
            <div className="relative flex-1 mr-8">
              <div className="flex items-center">
                <FiSearch
                  className="text-gray-400 mr-2 cursor-pointer"
                  size={20}
                  onClick={handleSearch}
                />
                <input
                  type="text"
                  placeholder="Search Employee"
                  className="w-full py-2 focus:outline-none text-base"
                  value={searchTerm}
                  onChange={handleSearchChange}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleSearch();
                    }
                  }}
                />
              </div>
              <div className="absolute right-0 top-0 bottom-0 border-r border-[#E4E4E4]"></div>
            </div>
            <div className="relative mx-7">
              <button
                onClick={() => setIsCalendarOpen(!isCalendarOpen)}
                className="border border-[#E4E4E4] rounded-2xl py-2 px-3 text-md cursor-pointer"
                type="button"
              >
                {formatDateRange()}
              </button>
              {isCalendarOpen && (
                <div
                  ref={calendarRef}
                  className="absolute right-0 top-12 z-50 bg-white shadow-lg rounded-lg border border-gray-200"
                >
                  <DateRangePicker
                    ranges={[
                      {
                        startDate: startDate || new Date(),
                        endDate: endDate || new Date(),
                        key: "selection",
                      },
                    ]}
                    onChange={handleDateRangeChange}
                    direction="horizontal"
                    moveRangeOnFirstSelection={false}
                  />
                </div>
              )}
            </div>
            <div className="relative" ref={dropdownRef}>
              <div 
                className="border border-[#E4E4E4] rounded-full p-2 flex items-center justify-between cursor-pointer min-w-[180px]"
                onClick={() => setIsDropdownOpen(!isDropdownOpen)}
              >
                <span className="text-gray-700">{reportType || "Select Report Type"}</span>
                <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                </svg>
              </div>
              {isDropdownOpen && (
                <div className="absolute mt-1 w-full bg-white border border-gray-200 rounded-lg shadow-lg z-10">
                  <div 
                    className="p-2 hover:bg-gray-100 cursor-pointer"
                    onClick={() => {
                      setReportType("Monthly");
                      setIsDropdownOpen(false);
                      handleReportTypeChange("Monthly");
                    }}
                  >
                    Monthly
                  </div>
                  <div 
                    className="p-2 hover:bg-gray-100 cursor-pointer"
                    onClick={() => {
                      setReportType("Weekly");
                      setIsDropdownOpen(false);
                      handleReportTypeChange("Weekly");
                    }}
                  >
                    Weekly
                  </div>
                  <div 
                    className="p-2 hover:bg-gray-100 cursor-pointer"
                    onClick={() => {
                      setReportType("Daily");
                      setIsDropdownOpen(false);
                      handleReportTypeChange("Daily");
                    }}
                  >
                    Daily
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Records Table */}
          <div className="mb-6">
            <div className="grid grid-cols-5 gap-4 px-4 py-2 bg-gray-50 rounded-t-lg">
              <div className="text-gray-600 font-medium">Employee Name</div>
              <div className="text-gray-600 font-medium">Start Date</div>
              <div className="text-gray-600 font-medium">End Date</div>
              <div className="text-gray-600 font-medium">Start Hour</div>
              <div className="text-gray-600 font-medium">End Hour</div>
            </div>
            <div className="divide-y divide-gray-100">
              {isLoading ? (
                <div className="flex justify-center py-8">
                  <div className="h-10 w-10 animate-spin rounded-full border-4 border-transparent border-t-orange-500 border-r-orange-500"></div>
                </div>
              ) : error ? (
                <div className="text-center py-4 text-red-500">Error loading employee data</div>
              ) : filteredRecords.length === 0 ? (
                <div className="text-center py-4">
                  No records found for {reportType} report
                </div>
              ) : (
                getCurrentRecords().map((record, index) => (
                  <div
                    key={index}
                    className="grid grid-cols-5 gap-4 px-4 py-3 hover:bg-gray-50 cursor-pointer"
                    onClick={() => handleRowClick(record)}
                  >
                    <div className="text-gray-800">{record.empName}</div>
                    <div className="text-gray-800">{formatDate(record.startDate)}</div>
                    <div className="text-gray-800">{record.endDate ? formatDate(record.endDate) : ""}</div>
                    <div className="text-gray-800">{record.startHour}</div>
                    <div className="text-gray-800">{record.endHour || ""}</div>
                  </div>
                ))
              )}
            </div>
            {!isLoading && !error && filteredRecords.length > 0 && (
              <div className="text-right text-sm text-gray-500 mt-2">
                Showing {Math.min((currentPage - 1) * recordsPerPage + 1, filteredRecords.length)} to {Math.min(currentPage * recordsPerPage, filteredRecords.length)} of {filteredRecords.length} records
              </div>
            )}
          </div>
        </div>
      </CustomModal>

      {/* Employee Monthly Report Modal - Render this AFTER the main modal */}
      {isMonthlyReportOpen && (
        <EmployeeMonthlyReport
          isOpen={isMonthlyReportOpen}
          onClose={() => {
            console.log("Closing EmployeeMonthlyReport modal");
            setIsMonthlyReportOpen(false);
          }}
          reportType={reportType}
          filteredData={filteredDataForReport}
        />
      )}

      {/* Employee Attendance Record Modal */}
      {isAttendanceModalOpen && (
        <AttendanceRecordModal
          isOpen={isAttendanceModalOpen}
          onClose={() => {
            console.log("Closing AttendanceRecordModal");
            setIsAttendanceModalOpen(false);
          }}
        />
      )}
    </>
  );
};

export default EmployeeAccessRecord;