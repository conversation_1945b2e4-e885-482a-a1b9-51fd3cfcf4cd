import React, { useState, useEffect } from 'react';
import { Search, Edit, Trash2, Plus } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import {
  useGetSitesQuery,
  useDeleteSiteMutation,
} from "../../../store/api/siteManagementApi";
import Swal from 'sweetalert2';
import { Tooltip } from "react-tooltip";
import "react-tooltip/dist/react-tooltip.css";


const AdminSiteManagement: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const navigate = useNavigate();

  // Get userId from localStorage
  const userId = localStorage.getItem('userId') || '';

  // Fetch sites using the API
  const { data: sites = [], isLoading, error, refetch } = useGetSitesQuery(userId);
  const [deleteSite, { isLoading: isDeleting }] = useDeleteSiteMutation();

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleAddNewSite = () => {
    navigate('/admin/tables-management/site-management/site-form');
  };

  const handleEditSite = (siteId: string) => {
    navigate(`/admin/tables-management/site-management/site-form/${siteId}`);
  };

  const handleDeleteSite = (siteId: string) => {
    Swal.fire({
      title: 'Are you sure?',
      text: 'Do you want to delete this site?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'No, cancel!',
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
    
      iconColor: '#ea580c',
     
     
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          await deleteSite(siteId).unwrap();
          Swal.fire({
            title: 'Deleted!',
            text: 'Site has been deleted successfully.',
            icon: 'success',
            confirmButtonColor: '#ea580c',
          });
          refetch();
        } catch (error) {
          console.error('Failed to delete site:', error);
          Swal.fire({
            title: 'Error!',
            text: 'Failed to delete site.',
            icon: 'error',
            confirmButtonColor: '#ea580c',
          });
        }
      }
    });
  };

  useEffect(() => {
    if (error) {
      Swal.fire({
        title: 'Error!',
        text: 'Failed to fetch sites.',
        icon: 'error',
        confirmButtonColor: '#ea580c',
      });
      console.error('Error fetching sites:', error);
    }
  }, [error]);

  // Filter sites based on search term
  const filteredSites = sites.filter(site => 
    site.siteName?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="p-4 bg-gray-50">
      {/* Header with search and add button */}
      <div className="flex flex-col md:flex-row justify-between items-center mb-6 p-2 rounded-xl border border-gray-200 bg-white">
        <h1 className="text-3xl font-bold text-gray-800 mb-4 sm:mb-0 p-3">Site Management</h1>
        
        <div className="flex md:flex-row md:space-y-0 space-y-3 flex-col w-full sm:w-auto space-x-2">
          <div className="relative flex-grow sm:flex-grow-0">
            <input
              type="text"
              placeholder="Search Sites"
              className="pl-3 pr-10 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 w-full"
              value={searchTerm}
              onChange={handleSearch}
            />
            <div className="absolute inset-y-0 right-0 flex items-center pr-3">
              <Search size={18} className="text-gray-400" />
            </div>
          </div>
          
          <button 
            onClick={handleAddNewSite}
            className="bg-orange-500 cursor-pointer hover:bg-orange-600 text-white px-4 py-2 rounded-lg flex items-center justify-center"
          >
            Add New Site <Plus size={18} className="ml-1" />
          </button>
        </div>
      </div>

      {/* Sites grid */}
      {isLoading ? (
         <div className="flex justify-center items-start bg-white h-screen pt-[30vh]">
            <div className="flex flex-col items-center">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500 mb-4"></div>
              <p className="text-gray-600 font-medium">Loading Site management...</p>
            </div>
          </div>
      ) : error ? (
        <div className="flex justify-center items-center h-40">
          <p className="text-red-500">Failed to load sites. Please try again later.</p>
        </div>
      ) : filteredSites.length === 0 ? (
        <div className="bg-white rounded-lg shadow p-6 text-center">
          <p className="text-gray-500">No sites found.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {filteredSites.map(site => (
            <div key={site._id} className="bg-white rounded-lg shadow overflow-hidden">
              {/* Image section with action buttons */}
              <div className="relative h-44 bg-gray-200">
                {site.siteImage ? (
                  <img 
                    src={site.siteImage} 
                    alt={site.siteName} 
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex flex-col items-center justify-center text-gray-400">
                    <svg className="w-16 h-16 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <p className="text-sm font-medium uppercase">No Image</p>
                    <p className="text-sm">Available</p>
                  </div>
                )}
                
                {/* Action buttons */}
                <div className="absolute top-2 right-2 flex space-x-1">
                  <button 
                    onClick={() => handleEditSite(site._id || '')}
                    className="p-2 bg-white rounded hover:bg-gray-100"
                  >
                   <Edit
                          id="edit-icon"
                          data-tooltip-id="edit-tooltip"
                          data-tooltip-content="Edit"
                          size={20}
                          className="cursor-pointer text-blue-500 hover:text-blue-700 transition-colors"
                        />
                        <Tooltip
                          id="edit-tooltip"
                          place="bottom"
                          className="!rounded-lg !bg-gray-800 !text-white !text-xs !px-3 !py-1 !shadow-lg"
                        />

                  </button>
                  <button 
                    onClick={() => handleDeleteSite(site._id || '')}
                    className="p-2 bg-white rounded hover:bg-gray-100"
                    disabled={isDeleting}
                  >
                                            <Trash2
                          id="delete-icon"
                          data-tooltip-id="delete-tooltip"
                          data-tooltip-content="Delete"
                          size={20}
                          className="cursor-pointer text-red-500 hover:text-red-700 transition-colors"
                        />
                        <Tooltip
                          id="delete-tooltip"
                          place="bottom"
                          className="!rounded-lg !bg-gray-800 !text-white !text-xs !px-2 !py-1 !shadow-lg"
                        />

                  </button>
                </div>
              </div>
              
              {/* Site info */}
              <div className="p-4">
                <h3 className="font-medium text-gray-800">{site.siteName}</h3>
                <div className="flex items-center justify-between text-gray-600 mt-1">
                  <span className="text-sm">{site.numberOfTables} Tables</span>
                  <span className={`text-xs px-3 py-2 rounded-lg ${site.isActive ? 'bg-green-50 text-green-500 border border-green-300' : 'bg-orange-50 text-orange-500 border border-orange-300'}`}>
                    {site.isActive ? 'Active' : 'Inactive'}
                  </span>
                </div>
                <p className="text-sm text-gray-500 mt-1 truncate">
                  {site.briefDescription}
                </p>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default AdminSiteManagement;