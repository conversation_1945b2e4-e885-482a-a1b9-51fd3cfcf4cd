import React from 'react';

interface ReceiptItem {
  name: string;
  quantity: number;
  price: number;
  total: number;
}

interface ReceiptProps {
  orderNo: string;
  table?: string;
  customer: string;
  date: string;
  items: ReceiptItem[];
  subtotal: number;
  tax: number;
  surcharge: number;
  total: number;
  payment: string;
  received: number;
  due: number;
  restaurantName?: string;
  restaurantAddress?: string;
  restaurantPhone?: string;
}

const Receipt: React.FC<ReceiptProps> = ({
  orderNo,
  table,
  customer,
  date,
  items,
  subtotal,
  tax,
  surcharge,
  total,
  payment,
  received,
  due,
  restaurantName = "Restaurant Name",
  restaurantAddress = "123 Main Street, City",
  restaurantPhone = "(*************"
}) => {
  return (
    <div className="receipt-container" style={{ 
      fontFamily: 'monospace', 
      fontSize: '12px', 
      width: '300px', 
      margin: '0 auto',
      padding: '10px',
      border: '1px dashed #000',
      backgroundColor: 'white'
    }}>
      {/* Header */}
      <div style={{ textAlign: 'center', marginBottom: '10px' }}>
        <div style={{ fontWeight: 'bold', fontSize: '14px' }}>{restaurantName}</div>
        <div>{restaurantAddress}</div>
        <div>Tel: {restaurantPhone}</div>
      </div>

      <div style={{ borderTop: '1px dashed #000', margin: '10px 0' }}></div>

      {/* Order Info */}
      <div style={{ marginBottom: '10px' }}>
        <div>Order No: {orderNo}</div>
        <div style={{ margin: '5px 0' }}></div>
        {table && <div>Table: {table}</div>}
        <div style={{ margin: '5px 0' }}></div>
        <div>Customer: {customer}</div>
        <div style={{ margin: '5px 0' }}></div>
        <div>Date: {date}</div>
      </div>

      <div style={{ borderTop: '1px dashed #000', margin: '10px 0' }}></div>

      {/* Items Header */}
      <div style={{ display: 'flex', justifyContent: 'space-between', fontWeight: 'bold' }}>
        <span>Item</span>
        <span>Total</span>
      </div>

      {/* Items */}
      {items.map((item, index) => (
        <div key={index} style={{ margin: '5px 0' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <span>{item.name} x{item.quantity}</span>
            <span>${item.total.toFixed(2)}</span>
          </div>
        </div>
      ))}

      <div style={{ borderTop: '1px dashed #000', margin: '10px 0' }}></div>

      {/* Totals */}
      <div style={{ marginBottom: '10px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <span>Subtotal:</span>
          <span>${subtotal.toFixed(2)}</span>
        </div>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <span>Tax:</span>
          <span>${tax.toFixed(2)}</span>
        </div>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <span>Surcharge:</span>
          <span>${surcharge.toFixed(2)}</span>
        </div>
        <div style={{ borderTop: '1px dashed #000', margin: '5px 0' }}></div>
        <div style={{ display: 'flex', justifyContent: 'space-between', fontWeight: 'bold' }}>
          <span>Total:</span>
          <span>${total.toFixed(2)}</span>
        </div>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <span>Payment:</span>
          <span>{payment}</span>
        </div>
        <div style={{ margin: '2px 0' }}>
          <span>//</span>
        </div>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <span>Received:</span>
          <span>${received.toFixed(2)}</span>
        </div>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <span>Received:</span>
          <span>${total.toFixed(2)}</span>
        </div>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <span>Due:</span>
          <span>${due.toFixed(2)}</span>
        </div>
      </div>

      <div style={{ borderTop: '1px dashed #000', margin: '10px 0' }}></div>

      {/* Footer */}
      <div style={{ textAlign: 'center', marginTop: '10px' }}>
        <div>Thank you for dining with us!</div>
      </div>
    </div>
  );
};

export default Receipt;
