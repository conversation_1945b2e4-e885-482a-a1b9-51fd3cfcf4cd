import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import {
  usePostTableMutation,
  usePutTableMutation,
  useGetTableQuery,
} from "../../../store/api/TableManagementApi";
import { useGetSitesQuery } from "../../../store/api/siteManagementApi";
import { toast } from "react-toastify";
import Swal from "sweetalert2";

const AdminTableForm = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEditMode = Boolean(id);
  const userId = localStorage.getItem("userId") || "";

  const [postTable, { isLoading: isCreating }] = usePostTableMutation();
  const [putTable, { isLoading: isUpdating }] = usePutTableMutation();

  const { data: tableData, isLoading: isLoadingTable } = useGetTableQuery(
    id || "",
    {
      skip: !isEditMode,
    }
  );

  const { data: sitesData, isLoading: isLoadingSites } =
    useGetSitesQuery(userId);

  const [formData, setFormData] = useState({
    tableName: "",
    seatingCapacity: "",
    tableNo: "",
    description: "",
    site: "",
    siteId: "",
    hasLampixDevice: false,
    isVisible: false,
  });

  useEffect(() => {
    if (isEditMode && tableData) {
      // Handle both string and object location types
      let siteNameValue = "";
      let siteIdValue = "";

      if (typeof tableData.location === "object" && tableData.location) {
        if ("siteName" in tableData.location) {
          siteNameValue = (tableData.location as { siteName: string }).siteName;
        }
        if ("_id" in tableData.location) {
          siteIdValue = (tableData.location as { _id: string })._id;
        }
      } else if (typeof tableData.location === "string") {
        // Try to match site ID from sites data
        const matchingSite = sitesData?.find(
          (site) => site._id === tableData.location
        );
        if (matchingSite) {
          siteNameValue = matchingSite.siteName;
          siteIdValue = matchingSite._id || "";
        } else {
          // If we can't find a matching site, use the string directly
          siteNameValue = String(tableData.location);
        }
      }

      setFormData({
        tableName: tableData.tableName || "",
        seatingCapacity: tableData.capacity?.toString() || "",
        tableNo: tableData.tableNo || "",
        description: tableData.description || "",
        site: siteNameValue,
        siteId: siteIdValue,
        hasLampixDevice:
          tableData.hasLampixDevice === true ||
          (typeof tableData.hasLampixDevice === "string" &&
            tableData.hasLampixDevice === "true"),
        isVisible: tableData.Status === "Available",
      });
    }
  }, [tableData, isEditMode, sitesData]);

  const handleChange = (e: { target: { name: any; value: any } }) => {
    const { name, value } = e.target;

    // Special handling for site selection to store both site name and ID
    if (name === "site") {
      const selectedSite = sitesData?.find((site) => site.siteName === value);
      setFormData((prev) => ({
        ...prev,
        [name]: value,
        siteId: selectedSite?._id || "",
      }));
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }
  };

  const handleToggle = () => {
    setFormData((prev) => ({ ...prev, hasLampixDevice: !prev.hasLampixDevice }));
  };

  const handleSubmit = async (e: { preventDefault: () => void }) => {
    e.preventDefault();

    try {
      if (!formData.tableName.trim()) {
        toast.error("Please enter table name");
        return;
      }

      if (!formData.seatingCapacity.trim()) {
        toast.error("Please enter seating capacity");
        return;
      }

      if (!formData.tableNo.trim()) {
        toast.error("Please enter table number");
        return;
      }

      if (!formData.site.trim() || !formData.siteId.trim()) {
        toast.error("Please select a site");
        return;
      }

      const capacity = parseInt(formData.seatingCapacity);
      if (isNaN(capacity) || capacity <= 0) {
        toast.error("Please enter a valid seating capacity");
        return;
      }

      const submitFormData = new FormData();

      if (isEditMode && id) {
        submitFormData.append("_id", id);
      } else {
        submitFormData.append("_id", "");
      }

      // Add all required fields
      submitFormData.append("tableName", formData.tableName);
      submitFormData.append("capacity", formData.seatingCapacity);
      submitFormData.append("tableNo", formData.tableNo);
      submitFormData.append("description", formData.description || "");

      // Use siteId instead of site name for location
      submitFormData.append("location", formData.siteId);

      // Convert boolean to string as expected by the backend
      submitFormData.append(
        "hasLampixDevice",
        formData.hasLampixDevice ? "true" : "false"
      );
      submitFormData.append("userId", userId);
      submitFormData.append(
        "Status",
        formData.isVisible ? "Available" : "Reserved"
      );

      // For debugging - log the form data
      console.log("Submitting form data:", {
        tableName: formData.tableName,
        capacity: formData.seatingCapacity,
        tableNo: formData.tableNo,
        description: formData.description || "",
        location: formData.siteId,
        hasLampixDevice: formData.hasLampixDevice ? "true" : "false",
        userId: userId,
        Status: formData.isVisible ? "Available" : "Reserved",
      });

      if (isEditMode && id) {
        await putTable({ id, data: submitFormData }).unwrap();
        Swal.fire({
          icon: "success",
          title: "Success!",
          text: "Table updated successfully!",
          confirmButtonColor: "#6366f1",
        });
      } else {
        await postTable(submitFormData).unwrap();
        Swal.fire({
          icon: "success",
          title: "Success!",
          text: "Table added successfully!",
          confirmButtonColor: "#6366f1",
        });
      }

      // Redirect to table management list
      navigate("/admin/tables-management/tables");
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error(
        `Failed to ${isEditMode ? "update" : "add"} table. Please try again.`
      );
    }
  };

  const handleCancel = () => {
    navigate("/admin/tables-management/tables");
  };

  // Function to render chairs/seats around the table based on capacity
  const renderTableWithSeats = () => {
    const capacity = parseInt(formData.seatingCapacity) || 0;
    const tableNumber = formData.tableNo || "T-No";

    if (capacity === 0) {
      return (
        <div className="border border-gray-400 rounded-md p-6 w-40 h-24 flex items-center justify-center">
          <div className="bg-orange-100 rounded-full w-20 h-20 flex items-center justify-center">
            <span className="text-gray-700 font-semibold text-xl">
              {tableNumber}
            </span>
          </div>
        </div>
      );
    }

    // Calculate positions for chairs around a circular/rectangular table
    const renderChairs = () => {
      const chairs = [];
      
      if (capacity === 1) {
        // Two chairs: top and bottom
        chairs.push(
          <div key="chair-1" className="absolute -top-4 left-1/2 transform -translate-x-1/2">
            <div className="w-10 h-3 bg-gray-50 rounded-md border border-gray-400"></div>
          </div>,
          <div key="chair-2" className="absolute -bottom-4 left-1/2 transform -translate-x-1/2">
            <div className="w-10 h-3 bg-gray-50 rounded-md border border-gray-400"></div>
          </div>
        );
      } else if (capacity === 2) {
        // Two chairs: top and bottom
        chairs.push(
           <div key="chair-1" className="absolute -top-4 left-1/2 transform -translate-x-1/2">
            <div className="w-10 h-3 bg-gray-50 rounded-md border border-gray-400"></div>
          </div>,
           <div key="chair-2" className="absolute -bottom-4 left-1/2 transform -translate-x-1/2">
            <div className="w-10 h-3 bg-gray-50 rounded-md border border-gray-400"></div>
          </div>
        );
      } else if (capacity === 3) {
        // Four chairs: top, bottom, left, right
        chairs.push(
          <div key="chair-1" className="absolute -top-4 left-1/2 transform -translate-x-1/2">
            <div className="w-10 h-3 bg-gray-50 rounded-md border border-gray-400"></div>
          </div>,
          <div key="chair-2" className="absolute -bottom-4 left-1/2 transform -translate-x-1/2">
            <div className="w-10 h-3 bg-gray-50 rounded-md border border-gray-400"></div>
          </div>,
          <div key="chair-3" className="absolute -left-4 top-1/2 transform -translate-y-1/2">
            <div className="w-3 h-10 bg-gray-50 rounded-md border border-gray-400"></div>
          </div>,
        );
      }else if (capacity === 4) {
        // Four chairs: top, bottom, left, right
        chairs.push(
          <div key="chair-1" className="absolute -top-4 left-1/2 transform -translate-x-1/2">
            <div className="w-10 h-3 bg-gray-50 rounded-md border border-gray-400"></div>
          </div>,
          <div key="chair-2" className="absolute -bottom-4 left-1/2 transform -translate-x-1/2">
            <div className="w-10 h-3 bg-gray-50 rounded-md border border-gray-400"></div>
          </div>,
          <div key="chair-3" className="absolute -left-4 top-1/2 transform -translate-y-1/2">
            <div className="w-3 h-10 bg-gray-50 rounded-md border border-gray-400"></div>
          </div>,
          <div key="chair-4" className="absolute -right-4 top-1/2 transform -translate-y-1/2">
            <div className="w-3 h-10 bg-gray-50 rounded-md border border-gray-400"></div>
          </div>
        );
      } else if (capacity === 5) {
        // Five chairs: top, bottom-left, bottom-right, left, right
        chairs.push(
          <div key="chair-1" className="absolute -top-4 left-1/2 transform -translate-x-1/2">
            <div className="w-10 h-3 bg-gray-50 rounded-md border border-gray-400"></div>
          </div>,
          <div key="chair-2" className="absolute -bottom-4 left-1/3 transform -translate-x-1/2">
            <div className="w-10 h-3 bg-gray-50 rounded-md border border-gray-400"></div>
          </div>,
          <div key="chair-3" className="absolute -bottom-4 right-1/3 transform translate-x-1/2">
            <div className="w-10 h-3 bg-gray-50 rounded-md border border-gray-400"></div>
          </div>,
          <div key="chair-4" className="absolute -left-4 top-1/2 transform -translate-y-1/2">
            <div className="w-3 h-10 bg-gray-50 rounded-md border border-gray-400"></div>
          </div>,
          <div key="chair-5" className="absolute -right-4 top-1/2 transform -translate-y-1/2">
            <div className="w-3 h-10 bg-gray-50 rounded-md border border-gray-400"></div>
          </div>
        );
      } else if (capacity === 6) {
        // Six chairs: top-left, top-right, left, right, bottom-left, bottom-right
        chairs.push(
          <div key="chair-1" className="absolute -top-4 left-1/3 transform -translate-x-1/2">
            <div className="w-10 h-3 bg-gray-50 rounded-md border border-gray-400"></div>
          </div>,
          <div key="chair-2" className="absolute -top-4 right-1/3 transform translate-x-1/2">
            <div className="w-10 h-3 bg-gray-50 rounded-md border border-gray-400"></div>
          </div>,
          <div key="chair-3" className="absolute -left-4 top-1/2 transform -translate-y-1/2">
            <div className="w-3 h-10 bg-gray-50 rounded-md border border-gray-400"></div>
          </div>,
          <div key="chair-4" className="absolute -right-4 top-1/2 transform -translate-y-1/2">
            <div className="w-3 h-10 bg-gray-50 rounded-md border border-gray-400"></div>
          </div>,
          <div key="chair-5" className="absolute -bottom-4 left-1/3 transform -translate-x-1/2">
            <div className="w-10 h-3 bg-gray-50 rounded-md border border-gray-400"></div>
          </div>,
          <div key="chair-6" className="absolute -bottom-4 right-1/3 transform translate-x-1/2">
            <div className="w-10 h-3 bg-gray-50 rounded-md border border-gray-400"></div>
          </div>
        );
      } else if (capacity === 7) {
        // Seven chairs: 2 top, 2 bottom, 2 sides, 1 extra on bottom
        chairs.push(
          <div key="chair-1" className="absolute -top-4 left-1/3 transform -translate-x-1/2">
            <div className="w-10 h-3 bg-gray-50 rounded-md border border-gray-400"></div>
          </div>,
          <div key="chair-2" className="absolute -top-4 right-1/3 transform translate-x-1/2">
            <div className="w-10 h-3 bg-gray-50 rounded-md border border-gray-400"></div>
          </div>,
          <div key="chair-3" className="absolute -left-4 top-1/2 transform -translate-y-1/2">
            <div className="w-3 h-10 bg-gray-50 rounded-md border border-gray-400"></div>
          </div>,
          <div key="chair-4" className="absolute -right-4 top-1/2 transform -translate-y-1/2">
            <div className="w-3 h-10 bg-gray-50 rounded-md border border-gray-400"></div>
          </div>,
          <div key="chair-5" className="absolute -bottom-4 left-1/4 transform -translate-x-1/2">
            <div className="w-10 h-3 bg-gray-50 rounded-md border border-gray-400"></div>
          </div>,
          <div key="chair-6" className="absolute -bottom-4 left-1/2 transform -translate-x-1/2">
            <div className="w-10 h-3 bg-gray-50 rounded-md border border-gray-400"></div>
          </div>,
          <div key="chair-7" className="absolute -bottom-4 right-1/4 transform translate-x-1/2">
            <div className="w-10 h-3 bg-gray-50 rounded-md border border-gray-400"></div>
          </div>
        );
      } else if (capacity >= 8) {
        // Eight or more chairs: distribute around the table
        const topChairs = Math.ceil(capacity / 4);
        const bottomChairs = Math.ceil(capacity / 4);
        const leftChairs = Math.floor((capacity - topChairs - bottomChairs) / 2);
        const rightChairs = capacity - topChairs - bottomChairs - leftChairs;

        // Top chairs
        for (let i = 0; i < topChairs; i++) {
          const leftPosition = (100 / (topChairs + 1)) * (i + 1);
          chairs.push(
            <div key={`chair-top-${i}`} className="absolute -top-4" style={{ left: `${leftPosition}%`, transform: 'translateX(-50%)' }}>
              <div className="w-10 h-3 bg-gray-50 rounded-md border border-gray-400"></div>
            </div>
          );
        }

        // Bottom chairs
        for (let i = 0; i < bottomChairs; i++) {
          const leftPosition = (100 / (bottomChairs + 1)) * (i + 1);
          chairs.push(
            <div key={`chair-bottom-${i}`} className="absolute -bottom-4" style={{ left: `${leftPosition}%`, transform: 'translateX(-50%)' }}>
              <div className="w-10 h-3 bg-gray-50 rounded-md border border-gray-400"></div>
            </div>
          );
        }

        // Left chairs
        for (let i = 0; i < leftChairs; i++) {
          const topPosition = (100 / (leftChairs + 1)) * (i + 1);
          chairs.push(
            <div key={`chair-left-${i}`} className="absolute -left-4" style={{ top: `${topPosition}%`, transform: 'translateY(-50%)' }}>
              <div className="w-3 h-10 bg-gray-50 rounded-md border border-gray-400"></div>
            </div>
          );
        }

        // Right chairs
        for (let i = 0; i < rightChairs; i++) {
          const topPosition = (100 / (rightChairs + 1)) * (i + 1);
          chairs.push(
            <div key={`chair-right-${i}`} className="absolute -right-4" style={{ top: `${topPosition}%`, transform: 'translateY(-50%)' }}>
              <div className="w-3 h-10 bg-gray-50 rounded-md border border-gray-400"></div>
            </div>
          );
        }
      }

      return chairs;
    };

    return (
      <div className="relative">
        <div className="border border-gray-400 rounded-md p-6 w-40 h-30 flex items-center justify-center">
          <div className="bg-orange-100 rounded-full w-20 h-20 flex items-center justify-center">
            <span className="text-gray-700 font-semibold text-xl">
              {tableNumber}
            </span>
          </div>
        </div>
        {renderChairs()}
      </div>
    );
  };

  if ((isEditMode && isLoadingTable) || isLoadingSites) {
    return (
      <div className="flex justify-center items-start bg-white h-screen pt-[38vh]">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500 mb-4"></div>
          <p className="text-gray-600 font-medium">Loading data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 rounded-lg shadow-sm w-full bg-gray-50 min-h-screen">
      <div className="flex items-center mb-6 p-2 rounded-xl bg-white border border-gray-200">
        <button
          className="flex items-center text-gray-700"
          onClick={handleCancel}
          type="button"
        >
          <svg
            className="w-5 h-5 mr-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 19l-7-7 7-7"
            />
          </svg>
          <span className="text-xl font-medium">
            {isEditMode ? "Edit Table" : "Add Table"}
          </span>
        </button>
      </div>

      <form onSubmit={handleSubmit} className="bg-white rounded-xl ">
        <div className="border border-gray-200 rounded-xl mb-6">
          <div className="bg-orange-50 px-4 py-3 rounded-xl mb-2">
            <h2 className="text-md font-medium text-gray-800">Table Details</h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6 p-3">
            <div>
              <label
                htmlFor="tableName"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Table Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="tableName"
                name="tableName"
                placeholder="Enter Table Name"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                value={formData.tableName}
                onChange={handleChange}
                required
              />
            </div>

            <div>
              <label
                htmlFor="seatingCapacity"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Seating Capacity <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="seatingCapacity"
                name="seatingCapacity"
                placeholder="Enter Seating Capacity"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                value={formData.seatingCapacity}
                onChange={handleChange}
                required
              />
            </div>

            <div>
              <label
                htmlFor="tableNo"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Table No <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="tableNo"
                name="tableNo"
                placeholder="Enter Table No"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                value={formData.tableNo}
                onChange={handleChange}
                required
              />
            </div>
            <div>
              <label
                htmlFor="site"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Site Information <span className="text-red-500">*</span>
              </label>
              <select
                id="site"
                name="site"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                value={formData.site}
                onChange={handleChange}
                required
              >
                <option value="">Select site</option>
                {sitesData &&
                  sitesData.map((site) => (
                    <option key={site._id} value={site.siteName}>
                      {site.siteName}
                    </option>
                  ))}
              </select>
              {/* Hidden field to store siteId */}
              <input type="hidden" name="siteId" value={formData.siteId} />
            </div>

            <div>
              <label
                htmlFor="description"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Description
              </label>
              <input
                type="text"
                id="description"
                name="description"
                placeholder="Enter Table Description"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                value={formData.description}
                onChange={handleChange}
              />
            </div>
          </div>
        </div>
        <div className="border border-gray-100 mt-2 rounded-2xl">
          <h2 className="text-base bg-orange-50 p-3 font-medium text-gray-800 mb-4">Permissions</h2>
           
          <div className="flex items-center p-2">
            <label className="inline-flex relative items-center cursor-pointer">
              <input 
                type="checkbox" 
                className="sr-only peer"
                id="hasLampixDevice"
                name="hasLampixDevice"
                checked={formData.hasLampixDevice}
                onChange={handleToggle}
              />
              <div className={`
                w-11 h-6 bg-gray-200 rounded-full peer 
                peer-checked:after:translate-x-full 
                peer-checked:after:border-white 
                after:content-[''] after:absolute after:top-0.5 after:left-[2px] 
                after:bg-white after:border-gray-300 after:border after:rounded-full 
                after:h-5 after:w-5 after:transition-all 
                ${formData.hasLampixDevice ? 'bg-orange-500' : ''}
              `}></div>
            </label>
            <span className="ml-3 text-sm font-medium text-gray-700">Visibility</span>
          </div>
        </div>
        <div className="mt-3 rounded-xl border border-gray-200">
          <h2 className="text-md font-medium text-gray-800 bg-orange-50 p-3 rounded-xl mb-3">
            Table Details
          </h2>
          <div className="flex justify-center mb-6 py-8">
            {renderTableWithSeats()}
          </div>
        </div>

        <div className="flex justify-end space-x-4 mt-3">
          <button
            type="button"
            onClick={handleCancel}
            className="border border-orange-500 cursor-pointer text-orange-500 hover:bg-orange-50 py-2 px-10 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isCreating || isUpdating}
            className={`bg-orange-500 ${
              isCreating || isUpdating ? "opacity-70" : "hover:bg-orange-600"
            } text-white py-2 px-10 cursor-pointer rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500`}
          >
            {isEditMode
              ? isUpdating
                ? "Updating..."
                : "Update Table"
              : isCreating
              ? "Adding..."
              : "Add Table"}
          </button>
        </div>
      </form>
    </div>
  );
};

export default AdminTableForm;